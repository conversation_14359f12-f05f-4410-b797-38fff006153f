<?php
require_once 'config/config.php';
require_once 'vendor/autoload.php';

use Wallet\FondsManager;
use Wallet\Calculator;

echo "🎬 Démonstration de l'Analyseur de Fonds\n";
echo "========================================\n\n";

try {
    $fondsManager = new FondsManager();
    $fonds = $fondsManager->getAllFonds();
    
    echo "📊 ANALYSE DU PORTEFEUILLE\n";
    echo "-------------------------\n";
    
    // Statistiques générales
    $stats = $fondsManager->getStatistics();
    echo "• Nombre total de fonds: " . (count($fonds)) . "\n";
    echo "• Note moyenne: " . $stats['note_moyenne'] . "/10\n";
    echo "• Performance moyenne: " . $stats['performance_moyenne'] . "%\n";
    echo "• Volatilité moyenne: " . $stats['volatilite_moyenne'] . "%\n";
    echo "• SRI moyen: " . $stats['sri_moyen'] . "\n\n";
    
    echo "🏆 TOP 5 DES MEILLEURS FONDS\n";
    echo "----------------------------\n";
    $topFonds = array_slice(array_filter($fonds, function($f) { 
        return $f['code_isin'] !== 'EURO000000000'; 
    }), 0, 5);
    
    foreach ($topFonds as $i => $fond) {
        $medal = ['🥇', '🥈', '🥉', '🏅', '🏅'][$i] ?? '🏅';
        echo "{$medal} {$fond['nom']}\n";
        echo "   Note: {$fond['note']}/10 | Performance: {$fond['performance_3ans']}% | SRI: {$fond['sri']}\n";
        echo "   Sharpe: {$fond['ratio_sharpe']} | Sortino: {$fond['ratio_sortino']} | Volatilité: {$fond['volatilite']}%\n\n";
    }
    
    echo "💡 PRÉCONISATION D'INVESTISSEMENT (1000€)\n";
    echo "==========================================\n";
    $recommendation = Calculator::generateRecommendation($fonds, 1000);
    
    echo "🏦 Fonds Euro (sécurité): " . number_format($recommendation['fonds_euro']['montant'], 0) . "€ (" . $recommendation['fonds_euro']['pourcentage'] . "%)\n\n";
    
    echo "📈 Fonds de croissance:\n";
    foreach ($recommendation['autres_fonds'] as $item) {
        echo "• " . $item['fond']['nom'] . "\n";
        echo "  Montant: " . number_format($item['montant'], 0) . "€ (" . $item['pourcentage'] . "%)\n";
        echo "  SRI: " . $item['fond']['sri'] . " | Note: " . $item['fond']['note'] . "/10\n\n";
    }
    
    echo "🎯 RÉSUMÉ DE LA PRÉCONISATION\n";
    echo "-----------------------------\n";
    echo "• Montant total: " . number_format($recommendation['montant_total'], 0) . "€\n";
    echo "• SRI total du portefeuille: " . $recommendation['sri_total'] . "\n";
    echo "• Nombre de fonds: " . (1 + count($recommendation['autres_fonds'])) . "\n";
    echo "• Diversification: ✅ Respectée (max 35% par fonds)\n";
    echo "• Objectif SRI: " . ($recommendation['sri_total'] >= 4 ? "✅ Atteint" : "⚠️ Non atteint") . " (cible: 4.0)\n\n";
    
    echo "🔍 ANALYSE DES RISQUES\n";
    echo "---------------------\n";
    $volatilitePortefeuille = 0;
    $montantTotal = $recommendation['montant_total'];
    
    // Calcul volatilité pondérée
    $volatilitePortefeuille += (0 * ($recommendation['fonds_euro']['montant'] / $montantTotal)); // Fonds Euro = 0% volatilité
    foreach ($recommendation['autres_fonds'] as $item) {
        $poids = $item['montant'] / $montantTotal;
        $volatilitePortefeuille += $item['fond']['volatilite'] * $poids;
    }
    
    echo "• Volatilité du portefeuille: " . number_format($volatilitePortefeuille, 1) . "%\n";
    echo "• Niveau de risque: " . ($volatilitePortefeuille < 10 ? "🟢 Faible" : ($volatilitePortefeuille < 15 ? "🟡 Modéré" : "🔴 Élevé")) . "\n";
    echo "• Diversification géographique: ✅ Internationale\n";
    echo "• Diversification sectorielle: ✅ Multi-secteurs\n\n";
    
    echo "📱 PROCHAINES ÉTAPES\n";
    echo "-------------------\n";
    echo "1. 🌐 Ouvrez http://localhost:8000 dans votre navigateur\n";
    echo "2. 📊 Explorez le tableau interactif des fonds\n";
    echo "3. 💰 Saisissez vos montants d'investissement\n";
    echo "4. 📈 Observez le calcul en temps réel du SRI pondéré\n";
    echo "5. 🎯 Consultez les préconisations personnalisées\n\n";
    
    echo "🎉 Démonstration terminée !\n";
    echo "L'application est prête à être utilisée.\n";
    
} catch (Exception $e) {
    echo "❌ Erreur: " . $e->getMessage() . "\n";
}
?>
