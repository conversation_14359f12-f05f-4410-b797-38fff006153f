<?php
require_once 'config/config.php';
require_once 'vendor/autoload.php';

use Wallet\QuantalysRobotService;
use Wallet\Database;
use Wallet\Calculator;

echo "🤖 Test du robot Quantalys\n";
echo "=========================\n\n";

// Codes ISIN de test (échantillon du contrat Floriane 2)
$testCodes = [
    'FR001400STS3',
    'FR0012287316', 
    'FR0012829364',
    'FR0013048287',
    'FR0013118692'
];

try {
    $robot = new QuantalysRobotService();
    $db = Database::getInstance();
    
    echo "🔍 Test avec " . count($testCodes) . " codes ISIN...\n\n";
    
    foreach ($testCodes as $index => $isin) {
        echo "📊 Test " . ($index + 1) . "/" . count($testCodes) . " - ISIN: {$isin}\n";
        echo "----------------------------------------\n";
        
        // Récupérer les données via le robot
        $fondData = $robot->getFondDataByISIN($isin);
        
        if (isset($fondData['error'])) {
            echo "❌ Erreur: " . $fondData['error'] . "\n\n";
            continue;
        }
        
        // Afficher les données récupérées
        echo "✅ Données récupérées:\n";
        echo "   • Nom: " . $fondData['nom'] . "\n";
        echo "   • Catégorie: " . $fondData['categorie'] . "\n";
        echo "   • Performance 3 ans: " . $fondData['performance_3ans'] . "%\n";
        echo "   • Volatilité: " . $fondData['volatilite'] . "%\n";
        echo "   • Ratio Sharpe: " . $fondData['ratio_sharpe'] . "\n";
        echo "   • Ratio Sortino: " . $fondData['ratio_sortino'] . "\n";
        echo "   • SRI: " . $fondData['sri'] . "\n";
        
        // Calculer la note
        $note = Calculator::calculateNote($fondData);
        echo "   • Note calculée: " . $note . "/10\n";
        
        // Mettre à jour en base de données
        try {
            $existing = $db->fetchOne("SELECT id FROM fonds WHERE code_isin = ?", [$isin]);
            
            $dataToSave = [
                'code_isin' => $isin,
                'nom' => $fondData['nom'],
                'categorie' => $fondData['categorie'],
                'performance_3ans' => $fondData['performance_3ans'],
                'volatilite' => $fondData['volatilite'],
                'ratio_sharpe' => $fondData['ratio_sharpe'],
                'ratio_sortino' => $fondData['ratio_sortino'],
                'sri' => $fondData['sri'],
                'note' => $note
            ];
            
            if ($existing) {
                $db->update('fonds', $dataToSave, 'code_isin = ?', [$isin]);
                echo "   📝 Fonds mis à jour en base de données\n";
            } else {
                $db->insert('fonds', $dataToSave);
                echo "   📝 Fonds ajouté en base de données\n";
            }
            
        } catch (Exception $e) {
            echo "   ❌ Erreur BDD: " . $e->getMessage() . "\n";
        }
        
        echo "\n";
        
        // Pause entre les requêtes pour éviter la surcharge
        if ($index < count($testCodes) - 1) {
            echo "⏳ Pause de 5 secondes...\n\n";
            sleep(5);
        }
    }
    
    echo "🎉 Test terminé !\n";
    echo "Consultez les logs dans logs/quantalys-robot.log pour plus de détails.\n";
    
} catch (Exception $e) {
    echo "❌ Erreur générale: " . $e->getMessage() . "\n";
}
?>
