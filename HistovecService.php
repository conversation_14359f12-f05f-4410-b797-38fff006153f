<?php

namespace Mat<PERSON><PERSON>ver\Services;

use HeadlessChromium\BrowserFactory;
use HeadlessChromium\Exception\DomException;
use HeadlessChromium\Exception\ElementNotFoundException;
use MatGyver\Entity\Dossier\Dossier;
use MatGyver\Helpers\Aws\Message;
use MatGyver\Helpers\Aws\Queue;
use MatGyver\Services\Dossier\DossierFactService;
use MatGyver\Services\Dossier\DossierService;
use MatGyver\Services\Dossier\DossierVehicleService;
use MatGyver\Services\Logger\LoggerService;
use MatGyver\Services\Vehicle\VehicleTypeService;

/**
 * Class HistovecService
 * @package MatGyver\Services
 */
class HistovecService
{
    const HISTOVEC_URL = 'https://histovec.interieur.gouv.fr/histovec/proprietaire';

    private DossierService $dossierService;
    private DossierFactService $dossierFactService;
    private HistovecResultParserService $histovecResultParserService;
    private VehicleTypeService $vehicleTypeService;

    /**
     * @param DossierService $dossierService
     * @param DossierFactService $dossierFactService
     * @param HistovecResultParserService $histovecResultParserService
     * @param VehicleTypeService $vehicleTypeService
     */
    public function __construct(
        DossierService $dossierService,
        DossierFactService $dossierFactService,
        HistovecResultParserService $histovecResultParserService,
        VehicleTypeService $vehicleTypeService
    ) {
        $this->dossierService = $dossierService;
        $this->dossierFactService = $dossierFactService;
        $this->histovecResultParserService = $histovecResultParserService;
        $this->vehicleTypeService = $vehicleTypeService;
    }

    /**
     * @param Dossier $dossier
     * @return void
     */
    public function checkHistovecStatus(Dossier $dossier): void
    {
        if ($dossier->getHistovecStatus() == Dossier::HISTOVEC_STATUS_OK) {
            return;
        }

        $canExecute = $this->canExecute($dossier);
        if (!$canExecute) {
            return;
        }

        $queue = new Queue('default');
        $message = new Message($queue, 'histovec', $dossier->getId());
        $message->send();
    }

    /**
     * @param Dossier $dossier
     * @param array $params
     * @return bool
     */
    public function canExecute(Dossier $dossier, array $params = []): bool
    {
        $vehicleOwner = $dossier->getVehicleOwner();
        if (!$vehicleOwner) {
            return false;
        }

        $firstName = $vehicleOwner->getFirstName();
        if (!$firstName) {
            $firstName = $params['first_name'] ?? '';
        }
        $lastName = $vehicleOwner->getLastName();
        if (!$lastName) {
            $lastName = $params['last_name'] ?? '';
        }
        $company = $vehicleOwner->getCompany();
        if (!$company) {
            $company = $params['company'] ?? '';
        }
        $hasName = (trim($firstName . ' ' . $lastName) or trim($company));
        if (!$hasName) {
            return false;
        }

        $vehicle = $dossier->getVehicle();
        if (!$vehicle) {
            return false;
        }
        if (!$vehicle->getRegistration() and (!isset($params['registration']) or !$params['registration'])) {
            return false;
        }
        if ($vehicle->getRegistrationCountry() != 'FR') {
            return false;
        }
        if (str_contains($vehicle->getRegistration(), '-') and !$vehicle->getFormuleNumber()) {
            return false;
        }

        return true;
    }

    /**
     * @param Dossier $dossier
     * @return string|null
     */
    public function getReason(Dossier $dossier): ?string
    {
        $vehicleOwner = $dossier->getVehicleOwner();
        if (!$vehicleOwner->getFirstName() or !$vehicleOwner->getLastName()) {
            return __('Les nom et prénom du propriétaire du véhicule sont nécessaires pour utiliser Histovec.');
        }

        $vehicle = $dossier->getVehicle();
        if (!$vehicle) {
            return __('Le numéro d\'immatriculation est nécessaire pour utiliser Histovec.');
        }
        if (!$vehicle->getRegistration()) {
            return __('Le numéro d\'immatriculation est nécessaire pour utiliser Histovec.');
        }
        if ($vehicle->getRegistrationCountry() != 'FR') {
            return __('L\'utilisation d\'Histovec ne fonctionne qu\'avec des plaques d\'immatriculation françaises.');
        }
        if (!$vehicle->getFormuleNumber()) {
            return __('Le numéro de formule est nécessaire pour utiliser Histovec.');
        }

        return null;
    }

    /**
     * @param Dossier $dossier
     * @param array $params
     * @return array
     */
    public function getHistovecHistory(Dossier $dossier, array $params = []): array
    {
        $canExecute = $this->canExecute($dossier, $params);
        if (!$canExecute) {
            return ['valid' => false, 'message' => __('Une information est manquante pour récupérer l\'historique sur Histovec.')];
        }

        $callHistovec = $this->callHistovec($dossier, $params);
        //LoggerService::logError(json_encode($callHistovec));
        if (!$callHistovec['valid']) {
            $dossier->setHistovecStatus(Dossier::HISTOVEC_STATUS_ERROR);
            try {
                $this->dossierService->persistAndFlush($dossier);
            } catch (\Exception $e) {
                return ['valid' => false, 'message' => __('Impossible de mettre à jour le dossier.')];
            }
            return $callHistovec;
        }

        $dossier->setHistovecStatus(Dossier::HISTOVEC_STATUS_OK);
        try {
            $this->dossierService->persistAndFlush($dossier);
        } catch (\Exception $e) {
            return ['valid' => false, 'message' => __('Impossible de mettre à jour le dossier.')];
        }

        $vehicle = $dossier->getVehicle();
        if ($callHistovec['dateFirstCirculation']) {
            $vehicle->setDateFirstCirculation($callHistovec['dateFirstCirculation']);
        }
        if ($callHistovec['dateRegistration']) {
            $vehicle->setRegistrationDate($callHistovec['dateRegistration']);
        }

        if ($callHistovec['histories']) {
            $this->dossierFactService->addHistovec($dossier, $callHistovec['histories']);
        }

        if ($callHistovec['vehicleInfos']) {
            $brand = '';
            foreach ($callHistovec['vehicleInfos'] as $vehicleInfo) {
                if ($vehicleInfo['unit'] == 'D.1') {
                    $brand = $vehicleInfo['value'];
                    $vehicle->setBrand($vehicleInfo['value']);
                }
                if ($vehicleInfo['unit'] == 'D.3') {
                    $model = $vehicleInfo['value'];
                    if ($brand) {
                        $model = trim(str_replace($brand, '', $model));
                    }
                    $vehicle->setModel($model);
                }
                if ($vehicleInfo['name'] == 'Couleur') {
                    $color = DossierVehicleService::findColorByName($vehicleInfo['value']);
                    if ($color) {
                        $vehicle->setColor($color);
                    }
                }
                /*if ($vehicleInfo['unit'] == 'E') {
                    $vehicle->setSerialNumber($vehicleInfo['value']);
                }*/
                if ($vehicleInfo['unit'] == 'J.1') {
                    $vehicleType = $this->vehicleTypeService->getRepository()->findOneBy(['code' => $vehicleInfo['value']]);
                    if ($vehicleType) {
                        $vehicle->setVehicleType($vehicleType->getName());
                    }
                }
                if ($vehicleInfo['unit'] == 'J.3') {
                    $vehicle->setBodywork($vehicleInfo['value']);
                }
                if ($vehicleInfo['unit'] == 'P.3') {
                    $engine = DossierVehicleService::findEngineByCode($vehicleInfo['value']);
                    if ($engine) {
                        $vehicle->setEngine($engine);
                    }
                }
                if ($vehicleInfo['unit'] == 'P.6') {
                    $vehicle->setPower($vehicleInfo['value']);
                }
                if ($vehicleInfo['unit'] == 'S.1') {
                    $vehicle->setSeating($vehicleInfo['value']);
                }
            }
        }

        try {
            $this->dossierService->persistAndFlush($vehicle);
        } catch (\Exception $e) {
            return ['valid' => false, 'message' => __('Impossible de mettre à jour le véhicule.')];
        }

        return ['valid' => true];
    }

    /**
     * @param Dossier $dossier
     * @param array $params
     * @return array
     */
    public function callHistovec(Dossier $dossier, array $params = []): array
    {
        $vehicleOwner = $dossier->getVehicleOwner();
        $vehicle = $dossier->getVehicle();

        $lastName = $vehicleOwner->getLastName();
        $firstName = $vehicleOwner->getFirstName();
        $company = $vehicleOwner->getCompany();
        $siren = $vehicleOwner->getSiren();
        $registration = $vehicle->getRegistration();
        $registrationDate = $vehicle->getRegistrationDate();
        $formuleNumber = $vehicle->getFormuleNumber();

        if ($params) {
            $lastName = $params['last_name'] ?? $lastName;
            $firstName = $params['first_name'] ?? $firstName;
            $company = $params['company'] ?? $company;
            $siren = $params['siren'] ?? $siren;
            $registration = $params['registration'] ?? $registration;
            $registrationDate = $params['registration_date'] ?? $registrationDate;
            $formuleNumber = $params['formule_number'] ?? $formuleNumber;
        }

        $config = [
            'userAgent' => 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/85.0.4183.121 Safari/537.36',
            'noSandbox' => true,
            'keepAlive' => false,
            'connectionTimeout' => 60000,
            'sendSyncDefaultTimeout' => 60000,
            'ignoreCertificateErrors' => true,
            'arguments' => [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-gpu',
                '--disable-software-rasterizer',
                '--disable-crash-reporter',
                '--disable-crashpad',
                '--crash-dumps-dir=/tmp',
                '--disable-background-networking',
                '--disable-default-apps',
                '--disable-extensions',
                '--disable-sync',
                '--disable-translate',
                '--hide-scrollbars',
                '--metrics-recording-only',
                '--mute-audio',
                '--no-first-run',
                '--no-zygote',
                '--single-process',
                '--disable-features=site-per-process',
                '--disable-features=NetworkService',
                '--disable-dev-shm-usage',
                '--disable-web-security',
                '--allow-running-insecure-content',
                '--disable-background-timer-throttling',  // Ajouté
                '--disable-backgrounding-occluded-windows', // Ajouté
                '--disable-breakpad',                    // Ajouté
                '--disable-component-extensions-with-background-pages', // Ajouté
                '--disable-features=TranslateUI',        // Ajouté
                '--disable-ipc-flooding-protection',     // Ajouté
                '--disable-renderer-backgrounding',      // Ajouté
                '--force-color-profile=srgb',           // Ajouté
                '--metrics-recording-only',             // Ajouté
                '--no-default-browser-check',           // Ajouté
                '--ignore-certificate-errors',          // Ajouté
                '--ignore-ssl-errors',                  // Ajouté
                '--ignore-certificate-errors-spki-list' // Ajouté
            ],
            //'headless' => false,
            //'keepAlive' => true,
            //'debugLogger' => 'php://stdout',
            'debugLogger' => '/tmp/chrome-debug.log',
        ];

        $chromePath = 'google-chrome-stable';
        if (ENV === ENV_DEV) {
            $chromePath = '/Applications/Google Chrome.app/Contents/MacOS/Google Chrome';
        }
        $browserFactory = new BrowserFactory($chromePath);
        $browser = $browserFactory->createBrowser($config);

        $registrationType = 'SIV';
        $isProfessional = ($company and $siren);
        $dateFirstCirculation = null;
        $dateRegistration = null;
        $vehicleInfos = '';
        $history1 = '';
        $history2 = '';

        if ($registrationDate and $registrationDate->format('Y') < 2009) {
            $registrationType = 'FNI';
        } elseif ($registrationDate and $registrationDate->format('Y') == 2009 and !str_contains($registration, '-')) {
            $registrationType = 'FNI';
        }

        $logData = [
            'lastName' => $lastName,
            'firstName' => $firstName,
            'company' => $company,
            'siren' => $siren,
            'registration' => $registration,
            'registrationDate' => $registrationDate,
            'formuleNumber' => $formuleNumber,
            'registrationType' => $registrationType,
            'isProfessional' => $isProfessional,
        ];

        try {
            $page = $browser->createPage();
            $navigation = $page->navigate(self::HISTOVEC_URL);
            $navigation->waitForNavigation();
            sleep(1);

            if ($registrationType == 'SIV') {
                $element = $page->dom()->querySelector('#SIV');
                $element->click();
                sleep(1);
                if ($isProfessional) {
                    $element = $page->dom()->querySelector('#siv-tab-1');
                    $element->click();
                    sleep(1);

                    $input = $page->dom()->querySelector('#form-siv-personne-morale-raison-sociale');
                    $input->sendKeys($company);

                    $input = $page->dom()->querySelector('#form-siv-personne-morale-numero-siren');
                    $input->sendKeys($siren);

                    $input = $page->dom()->querySelector('#form-siv-personne-morale-numero-immatriculation');
                    $input->sendKeys($registration);

                    $input = $page->dom()->querySelector('#form-siv-personne-morale-numero-formule');
                    $input->sendKeys($formuleNumber);
                } else {
                    $input = $page->dom()->querySelector('#form-siv-particulier-nom-naissance');
                    $input->sendKeys($lastName);

                    $input = $page->dom()->querySelector('#form-siv-particulier-prenom');
                    $input->sendKeys($firstName);

                    $input = $page->dom()->querySelector('#form-siv-particulier-numero-immatriculation');
                    $input->sendKeys($registration);

                    $input = $page->dom()->querySelector('#form-siv-particulier-numero-formule');
                    $input->sendKeys($formuleNumber);
                }
            } elseif ($registrationType == 'FNI') {
                $element = $page->dom()->querySelector('#FNI');
                $element->click();
                sleep(1);

                if ($isProfessional == 1) {
                    $element = $page->dom()->querySelector('#fni-tab-1');
                    $element->click();
                    sleep(1);

                    $input = $page->dom()->querySelector('#form-fni-personne-morale-raison-sociale');
                    $input->sendKeys($company);

                    $input = $page->dom()->querySelector('#form-fni-personne-morale-numero-siren');
                    $input->sendKeys($siren);

                    $input = $page->dom()->querySelector('#form-fni-personne-morale-numero-immatriculation');
                    $input->sendKeys($registration);

                    $input = $page->dom()->querySelector('#form-fni-personne-morale-date-emission');
                    $input->sendKeys($formuleNumber);
                } else {
                    $input = $page->dom()->querySelector('#form-fni-particulier-nom-prenom');
                    $input->sendKeys($lastName . ' ' . $firstName);

                    $input = $page->dom()->querySelector('#form-fni-particulier-numero-immatriculation');
                    $input->sendKeys($registration);

                    $input = $page->dom()->querySelector('#form-fni-particulier-date-emission');
                    $input->sendKeys($registrationDate->format('d/m/Y'));
                }
            }

            $element = $page->dom()->querySelector('#bouton-recherche');
            $disabled = $element->getAttribute('aria-disabled');
            if ($disabled == 'true') {
                //LoggerService::logError('histovec : #bouton-recherche is disabled');
                //LoggerService::logError(json_encode($logData));
                return ['valid' => false, 'message' => __('Données incorrectes pour rechercher ce véhicule.')];
            }

            $element->click();
            $navigation->waitForNavigation();
            sleep(2);

            $content = $page->dom()->getText();
            if (str_contains($content, 'Véhicule non trouvé') or str_contains($content, 'Ce véhicule est inconnu')) {
                return ['valid' => false, 'message' => __('Impossible de trouver ce véhicule.')];
            }

            /*try {
                $element = $page->dom()->querySelector('.fr-error-subtitle');
                if ($element) {
                    $content = $element->getText();
                    //LoggerService::logError('histovec : contenu de div.fr-error-subtitle : ' . $content);
                    //LoggerService::logError(json_encode($logData));
                    if ($content == 'Erreur 404') {
                        return ['valid' => false, 'message' => __('Impossible de trouver ce véhicule.')];
                    }
                }
            } catch (ElementNotFoundException $e) {}*/


            //Date de première immatriculation
            try {
                //$element = $page->dom()->querySelector('#valeur-date-immatriculation');
                $element = $page->dom()->querySelector('#valeur-date-immatriculation-normal');
                if (!$element) {
                    $element = $page->dom()->querySelector('#valeur-date-immatriculation-france-normal');
                }
                if (!$element) {
                    LoggerService::logError('Unable to find element #valeur-date-immatriculation-normal');
                    LoggerService::logError(json_encode($logData));
                } else {
                    $date = $element->getText();
                    if ($date) {
                        try {
                            $dateFirstCirculation = createDateFromFormat('d/m/Y', $date);
                        } catch (\Exception $e) {
                            LoggerService::logError('Unable to parse date ' . $date . ' : ' . $e->getMessage());
                            LoggerService::logError(json_encode($logData));
                        }
                    }
                }
            } catch (ElementNotFoundException $e) {}


            //Date de la carte grise
            try {
                //$element = $page->dom()->querySelector('#valeur-date-certificat');
                $element = $page->dom()->querySelector('#valeur-date-certificat-normal');
                if (!$element) {
                    LoggerService::logError('Unable to find element #valeur-date-certificat-normal');
                    LoggerService::logError(json_encode($logData));
                } else {
                    $date = $element->getText();
                    if ($date) {
                        try {
                            $dateRegistration = createDateFromFormat('d/m/Y', $date);
                        } catch (\Exception $e) {
                            LoggerService::logError('Unable to parse date ' . $date . ' : ' . $e->getMessage());
                            LoggerService::logError(json_encode($logData));
                        }
                    }
                }
            } catch (ElementNotFoundException $e) {}


            //vehicle infos
            try {
                $element = $page->dom()->querySelector('#report-tab-content-1 .fr-grid-row');
                if (!$element) {
                    LoggerService::logError('Unable to find element #report-tab-content-1 .fr-grid-row');
                    LoggerService::logError(json_encode($logData));
                } else {
                    $vehicleInfos = $element->getHTML();
                }
            } catch (ElementNotFoundException $e) {
                LoggerService::logError('Unable to get element #report-tab-content-1 .fr-grid-row ' . $e->getMessage());
                LoggerService::logError(json_encode($logData));
            }


            //history 1
            try {
                $element = $page->dom()->querySelector('#report-tab-content-4 .fr-grid-row');
                if (!$element) {
                    LoggerService::logError('Unable to find element #report-tab-content-4 .fr-grid-row');
                    LoggerService::logError(json_encode($logData));
                } else {
                    $history1 = $element->getHTML();
                }
            } catch (ElementNotFoundException $e) {
                LoggerService::logError('Unable to get element #report-tab-content-4 .fr-grid-row ' . $e->getMessage());
                LoggerService::logError(json_encode($logData));
            }

            //history 2
            try {
                $element = $page->dom()->querySelector('#report-tab-content-5 .fr-grid-row');
                if (!$element) {
                    LoggerService::logError('Unable to find element #report-tab-content-5 .fr-grid-row');
                    LoggerService::logError(json_encode($logData));
                } else {
                    $history2 = $element->getHTML();
                }
            } catch (ElementNotFoundException $e) {
                LoggerService::logError('Unable to get element #report-tab-content-5 .fr-grid-row ' . $e->getMessage());
                LoggerService::logError(json_encode($logData));
            }
        } catch (ElementNotFoundException $e) {
            LoggerService::logError('element not found : ' . $e->getMessage());
            LoggerService::logError(json_encode($logData));
            return ['valid' => false, 'message' => __('Une erreur est survenue, merci de réessayer.')];
        } catch (DomException $e) {
            LoggerService::logError('Dom exception : ' . $e->getMessage());
            LoggerService::logError(json_encode($logData));
            return ['valid' => false, 'message' => __('Une erreur est survenue, merci de réessayer.')];
        } catch (\Exception $e) {
            LoggerService::logError($e->getMessage());
            LoggerService::logError(json_encode($logData));
            return ['valid' => false, 'message' => $e->getMessage()];
        } finally {
            // bye
            $browser->close();
        }

        $histories1 = $this->postProcessHistory1($history1);
        $histories2 = $this->postProcessHistory2($history2);
        $histories = array_merge($histories1, $histories2);
        //$history = $this->postProcess($histories);

        $vehicleInfos = $this->postProcessVehicleInfos($vehicleInfos);
        //LoggerService::logError(json_encode($logData));

        return [
            'valid' => true,
            'dateFirstCirculation' => $dateFirstCirculation,
            'dateRegistration' => $dateRegistration,
            'histories' => $histories,
            'vehicleInfos' => $vehicleInfos,
        ];
    }

    /**
     * @param array $histories
     * @return string
     */
    private function postProcess(array $histories): string
    {
        if (!$histories) {
            return '';
        }

        ksort($histories);
        $history = '';
        foreach ($histories as $timestamp => $content) {
            $history .= date('d/m/Y', $timestamp) . ' : ' . $content . "\n";
        }

        return $history;
    }

    /**
     * @param string $history1
     * @return array
     */
    private function postProcessHistory1(string $history1): array
    {
        $historiesToParse = [$history1];
        if (str_contains($history1, 'Historique des opérations à l\'étranger')) {
            $explode = explode('Historique des opérations en France', $history1);

            $historiesToParse = [
                $explode[0] . '</caption></table></div></div>',
                '<div><div><table><caption>Titre' . $explode[1],
            ];
        }

        $histories = [];
        foreach ($historiesToParse as $historyToParse) {
            $this->histovecResultParserService->setDoc($historyToParse);
            $this->histovecResultParserService->setNbColumns(2);
            $this->histovecResultParserService->setRemoveTitle(false);
            $this->histovecResultParserService->setRemoveColumnNames(false);
            $results = $this->histovecResultParserService->parse();

            foreach ($results as $result) {
                if (!$result or !isset($result[1])) {
                    continue;
                }
                $content = $result[1];
                $validateDate = validateDate($result[0], 'd/m/Y');
                if ($validateDate) {
                    $date = createDateFromFormat('d/m/Y', $result[0]);
                    $histories[] = [
                        'date' => $date,
                        'content' => $content
                    ];
                }
            }
        }

        return $histories;
    }

    /**
     * @param string $history2
     * @return array
     */
    private function postProcessHistory2(string $history2): array
    {
        $this->histovecResultParserService->setDoc($history2);
        $this->histovecResultParserService->setNbColumns(4);
        $this->histovecResultParserService->setRemoveTitle(false);
        $this->histovecResultParserService->setRemoveColumnNames(false);
        $results = $this->histovecResultParserService->parse();

        $histories = [];
        foreach ($results as $result) {
            $history = [
                'content' => $result[1]
            ];
            if ($result[2]) {
                $history['content'] .= ' (' . $result[2] . ')';
            }
            if ($result[3]) {
                $history['mileage'] = str_replace([' ', ',', 'km', '\u202f'], '', $result[3]);
                $history['mileage'] = str_replace('&#8239;', '', $history['mileage']);
                $history['mileage'] = str_replace('\xE2\x80\xAF', '', $history['mileage']);

                $history['mileage'] = preg_replace("/\xE2\x80\xAF/", '', $history['mileage']);
            }

            $validateDate = validateDate($result[0], 'd/m/Y');
            if ($validateDate) {
                $date = createDateFromFormat('d/m/Y', $result[0]);
                $history['date'] = $date;
                $histories[] = $history;
            }
        }

        return $histories;
    }

    /**
     * @param string $vehicleInfos
     * @return array
     */
    private function postProcessVehicleInfos(string $vehicleInfos): array
    {
        $this->histovecResultParserService->setDoc($vehicleInfos);
        $this->histovecResultParserService->setNbColumns(3);
        $this->histovecResultParserService->setRemoveTitle(false);
        $this->histovecResultParserService->setRemoveColumnNames(false);
        $results = $this->histovecResultParserService->parse();

        $vehicleInfos = [];
        foreach ($results as $result) {
            $vehicleInfos[] = [
                'name' => $result[0],
                'unit' => $result[1],
                'value' => $result[2],
            ];
        }

        return $vehicleInfos;
    }
}
