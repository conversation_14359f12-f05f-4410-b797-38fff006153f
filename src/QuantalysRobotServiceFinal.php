<?php

namespace Wallet;

use HeadlessChromium\BrowserFactory;
use HeadlessChromium\Exception\DomException;
use HeadlessChromium\Exception\ElementNotFoundException;

/**
 * Class QuantalysRobotServiceFinal
 * Robot final pour récupérer automatiquement les données des fonds depuis Quantalys
 * Basé sur l'analyse du fichier HTML de test
 */
class QuantalysRobotServiceFinal
{
    const QUANTALYS_BASE_URL = 'https://www.quantalys.com';
    const QUANTALYS_SEARCH_URL = 'https://www.quantalys.com/Fonds';
    
    private $logFile;
    private $browser;
    private $page;
    
    public function __construct()
    {
        $this->logFile = __DIR__ . '/../logs/quantalys-robot.log';
        $this->ensureLogDirectory();
    }
    
    /**
     * Récupère les données d'un fonds par son code ISIN
     */
    public function getFondDataByISIN(string $isin): array
    {
        $this->log("Début récupération pour ISIN: {$isin}");
        
        try {
            $this->initBrowser();
            
            // Construire l'URL directe du fonds
            $fondUrl = $this->buildFondUrl($isin);
            
            // Accéder à la page du fonds
            $fondData = $this->extractFondData($fondUrl, $isin);
            
            $this->closeBrowser();
            
            $this->log("Données récupérées avec succès pour ISIN: {$isin}");
            return $fondData;
            
        } catch (\Exception $e) {
            $this->log("Erreur pour ISIN {$isin}: " . $e->getMessage());
            $this->closeBrowser();
            return ['error' => $e->getMessage()];
        }
    }
    
    /**
     * Construit l'URL directe d'un fonds Quantalys
     */
    private function buildFondUrl(string $isin): string
    {
        // Format typique des URLs Quantalys pour les fonds
        return self::QUANTALYS_BASE_URL . '/Fonds/' . $isin;
    }
    
    /**
     * Initialise le navigateur
     */
    private function initBrowser(): void
    {
        $chromePath = '/Applications/Google Chrome.app/Contents/MacOS/Google Chrome';
        
        // Détecter le chemin de Chrome selon l'OS
        if (!file_exists($chromePath)) {
            $chromePath = 'google-chrome-stable';
        }
        
        $config = [
            'userAgent' => 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'noSandbox' => true,
            'keepAlive' => false,
            'connectionTimeout' => 60000,
            'sendSyncDefaultTimeout' => 60000,
            'ignoreCertificateErrors' => true,
            'arguments' => [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-gpu',
                '--disable-software-rasterizer',
                '--disable-crash-reporter',
                '--disable-background-networking',
                '--disable-default-apps',
                '--disable-extensions',
                '--disable-sync',
                '--disable-translate',
                '--hide-scrollbars',
                '--no-first-run',
                '--disable-web-security',
                '--allow-running-insecure-content',
                '--disable-blink-features=AutomationControlled'
            ],
            'headless' => true
        ];
        
        $browserFactory = new BrowserFactory($chromePath);
        $this->browser = $browserFactory->createBrowser($config);
        $this->page = $this->browser->createPage();
    }
    
    /**
     * Ferme le navigateur
     */
    private function closeBrowser(): void
    {
        if ($this->browser) {
            $this->browser->close();
            $this->browser = null;
            $this->page = null;
        }
    }
    
    /**
     * Extrait les données du fonds depuis sa page
     */
    private function extractFondData(string $fondUrl, string $isin): array
    {
        $this->log("Extraction des données depuis: {$fondUrl}");
        
        // Naviguer vers la page du fonds
        $navigation = $this->page->navigate($fondUrl);
        $navigation->waitForNavigation();
        sleep(3);
        
        // Accepter les cookies si nécessaire
        $this->acceptCookies();
        
        // Récupérer le contenu HTML
        $pageContent = $this->page->getHtml();
        
        $fondData = [
            'isin' => $isin,
            'nom' => $this->extractName($pageContent),
            'categorie' => $this->extractCategory($pageContent),
            'performance_3ans' => $this->extractPerformance($pageContent),
            'volatilite' => $this->extractVolatility($pageContent),
            'ratio_sharpe' => $this->extractSharpe($pageContent),
            'ratio_sortino' => $this->extractSortino($pageContent),
            'sri' => $this->extractSRI($pageContent)
        ];
        
        // Nettoyer et valider les données
        $fondData = $this->cleanFondData($fondData);
        
        return $fondData;
    }
    
    /**
     * Extrait le nom du fonds
     */
    private function extractName(string $content): string
    {
        if (preg_match('/<h1[^>]*>.*?<strong[^>]*>([^<]+)<\/strong>/s', $content, $matches)) {
            return trim($matches[1]);
        }
        if (preg_match('/<h1[^>]*>([^<]+)<\/h1>/s', $content, $matches)) {
            $name = trim($matches[1]);
            // Nettoyer le nom
            $name = preg_replace('/^OPCVM\s*-\s*/', '', $name);
            $name = preg_replace('/\s*-\s*[A-Z]{2}[0-9A-Z]{10}.*$/', '', $name);
            return $name;
        }
        return 'Nom non trouvé';
    }
    
    /**
     * Extrait la catégorie du fonds
     */
    private function extractCategory(string $content): string
    {
        if (preg_match('/Catégorie Quantalys.*?<dd[^>]*>.*?<a[^>]*>([^<]+)<\/a>/s', $content, $matches)) {
            return trim(strip_tags($matches[1]));
        }
        if (preg_match('/Classification AMF.*?<dd[^>]*>.*?<a[^>]*>([^<]+)<\/a>/s', $content, $matches)) {
            return trim(strip_tags($matches[1]));
        }
        return 'Non classifié';
    }
    
    /**
     * Extrait la performance sur 3 ans
     */
    private function extractPerformance(string $content): float
    {
        // Chercher dans les données du graphique JSON
        if (preg_match('/dataProvider.*?(\[.*?\])/s', $content, $matches)) {
            try {
                $jsonData = $matches[1];
                $data = json_decode($jsonData, true);
                
                if ($data && is_array($data) && count($data) > 1) {
                    $firstValue = $data[0]['y_0'] ?? 100;
                    $lastValue = end($data)['y_0'] ?? 100;
                    
                    if ($firstValue > 0) {
                        return round((($lastValue / $firstValue) - 1) * 100, 2);
                    }
                }
            } catch (\Exception $e) {
                $this->log("Erreur parsing données graphique: " . $e->getMessage());
            }
        }
        
        // Chercher dans le texte
        if (preg_match('/performance[^0-9]*([0-9,\.\-]+)%/i', $content, $matches)) {
            return $this->parsePercentage($matches[1]);
        }
        
        return 0.0;
    }
    
    /**
     * Extrait la volatilité
     */
    private function extractVolatility(string $content): float
    {
        if (preg_match('/volatilité[^0-9]*([0-9,\.]+)%?/i', $content, $matches)) {
            return $this->parsePercentage($matches[1]);
        }
        return 0.0;
    }
    
    /**
     * Extrait le ratio de Sharpe
     */
    private function extractSharpe(string $content): float
    {
        if (preg_match('/sharpe[^0-9]*([0-9,\.\-]+)/i', $content, $matches)) {
            return $this->parseDecimal($matches[1]);
        }
        return 0.0;
    }
    
    /**
     * Extrait le ratio de Sortino
     */
    private function extractSortino(string $content): float
    {
        if (preg_match('/sortino[^0-9]*([0-9,\.\-]+)/i', $content, $matches)) {
            return $this->parseDecimal($matches[1]);
        }
        return 0.0;
    }
    
    /**
     * Extrait le SRI
     */
    private function extractSRI(string $content): int
    {
        // Basé sur l'évaluation ISR
        if (strpos($content, 'Non ISR') !== false) {
            return 3; // Score faible pour "Non ISR"
        }
        
        // Chercher un score SRI explicite
        if (preg_match('/sri[^0-9]*([0-9]+)/i', $content, $matches)) {
            return (int) $matches[1];
        }
        
        // Basé sur SFDR
        if (strpos($content, 'Article 8') !== false || strpos($content, 'Article 9') !== false) {
            return 7; // Score élevé pour fonds durables
        }
        
        return 5; // Valeur par défaut
    }
    
    /**
     * Accepte les cookies si nécessaire
     */
    private function acceptCookies(): void
    {
        try {
            $cookieSelectors = [
                '#cookie-accept',
                '.cookie-accept',
                'button[data-accept="cookies"]',
                '.btn-accept-cookies'
            ];
            
            foreach ($cookieSelectors as $selector) {
                try {
                    $element = $this->page->dom()->querySelector($selector);
                    if ($element) {
                        $element->click();
                        sleep(1);
                        break;
                    }
                } catch (ElementNotFoundException $e) {
                    continue;
                }
            }
        } catch (\Exception $e) {
            // Ignorer les erreurs de cookies
        }
    }
    
    /**
     * Parse un pourcentage depuis un texte
     */
    private function parsePercentage(string $text): float
    {
        $text = str_replace(['%', ' ', ','], ['', '', '.'], $text);
        $text = preg_replace('/[^\d\.\-\+]/', '', $text);
        
        if (is_numeric($text)) {
            return (float) $text;
        }
        
        return 0.0;
    }
    
    /**
     * Parse un nombre décimal depuis un texte
     */
    private function parseDecimal(string $text): float
    {
        $text = str_replace([' ', ','], ['', '.'], $text);
        $text = preg_replace('/[^\d\.\-\+]/', '', $text);
        
        if (is_numeric($text)) {
            return (float) $text;
        }
        
        return 0.0;
    }
    
    /**
     * Nettoie les données du fonds
     */
    private function cleanFondData(array $data): array
    {
        // Valeurs par défaut si données manquantes
        $defaults = [
            'nom' => 'Fonds inconnu',
            'categorie' => 'Non classifié',
            'performance_3ans' => 0.0,
            'volatilite' => 15.0,
            'ratio_sharpe' => 0.5,
            'ratio_sortino' => 0.7,
            'sri' => 5
        ];
        
        foreach ($defaults as $key => $default) {
            if (!isset($data[$key]) || empty($data[$key])) {
                $data[$key] = $default;
            }
        }
        
        // Validation des valeurs
        $data['performance_3ans'] = max(-50, min(100, $data['performance_3ans']));
        $data['volatilite'] = max(0, min(100, $data['volatilite']));
        $data['ratio_sharpe'] = max(-5, min(5, $data['ratio_sharpe']));
        $data['ratio_sortino'] = max(-5, min(5, $data['ratio_sortino']));
        $data['sri'] = max(1, min(10, $data['sri']));
        
        return $data;
    }
    
    /**
     * Crée le répertoire de logs si nécessaire
     */
    private function ensureLogDirectory(): void
    {
        $logDir = dirname($this->logFile);
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
    }
    
    /**
     * Écrit dans le fichier de log
     */
    private function log(string $message): void
    {
        $timestamp = date('Y-m-d H:i:s');
        $logMessage = "[{$timestamp}] {$message}\n";
        file_put_contents($this->logFile, $logMessage, FILE_APPEND | LOCK_EX);
    }
}
