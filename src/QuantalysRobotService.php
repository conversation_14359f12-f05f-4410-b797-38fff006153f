<?php

namespace Wallet;

use HeadlessChromium\BrowserFactory;
use HeadlessChromium\Exception\DomException;
use HeadlessChromium\Exception\ElementNotFoundException;

/**
 * Class QuantalysRobotService
 * Robot pour récupérer automatiquement les données des fonds depuis Quantalys
 */
class QuantalysRobotService
{
    const QUANTALYS_BASE_URL = 'https://www.quantalys.com';
    const QUANTALYS_SEARCH_URL = 'https://www.quantalys.com/Fonds';
    
    private $logFile;
    
    public function __construct()
    {
        $this->logFile = __DIR__ . '/../logs/quantalys-robot.log';
        $this->ensureLogDirectory();
    }
    
    /**
     * Récupère les données d'un fonds par son code ISIN
     */
    public function getFondDataByISIN(string $isin): array
    {
        $this->log("Début récupération pour ISIN: {$isin}");
        
        try {
            $browser = $this->createBrowser();
            $page = $browser->createPage();
            
            // Étape 1: Rechercher le fonds
            $fondUrl = $this->searchFond($page, $isin);
            if (!$fondUrl) {
                $this->log("Fonds non trouvé pour ISIN: {$isin}");
                return ['error' => "Fonds non trouvé pour l'ISIN: {$isin}"];
            }
            
            // Étape 2: Accéder à la page du fonds
            $fondData = $this->extractFondData($page, $fondUrl, $isin);
            
            $browser->close();
            
            $this->log("Données récupérées avec succès pour ISIN: {$isin}");
            return $fondData;
            
        } catch (\Exception $e) {
            $this->log("Erreur pour ISIN {$isin}: " . $e->getMessage());
            return ['error' => $e->getMessage()];
        }
    }
    
    /**
     * Récupère les données de plusieurs fonds
     */
    public function getMultipleFondsData(array $isins, int $maxConcurrent = 3): array
    {
        $results = [];
        $chunks = array_chunk($isins, $maxConcurrent);
        
        foreach ($chunks as $chunk) {
            foreach ($chunk as $isin) {
                $results[$isin] = $this->getFondDataByISIN($isin);
                
                // Pause entre les requêtes pour éviter la détection
                sleep(rand(2, 5));
            }
            
            // Pause plus longue entre les chunks
            if (count($chunks) > 1) {
                sleep(rand(10, 20));
            }
        }
        
        return $results;
    }
    
    /**
     * Crée une instance de navigateur Chrome
     */
    private function createBrowser()
    {
        $config = [
            'userAgent' => 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'noSandbox' => true,
            'keepAlive' => false,
            'connectionTimeout' => 60000,
            'sendSyncDefaultTimeout' => 60000,
            'ignoreCertificateErrors' => true,
            'arguments' => [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-gpu',
                '--disable-software-rasterizer',
                '--disable-crash-reporter',
                '--disable-crashpad',
                '--crash-dumps-dir=/tmp',
                '--disable-background-networking',
                '--disable-default-apps',
                '--disable-extensions',
                '--disable-sync',
                '--disable-translate',
                '--hide-scrollbars',
                '--metrics-recording-only',
                '--mute-audio',
                '--no-first-run',
                '--no-zygote',
                '--single-process',
                '--disable-features=site-per-process',
                '--disable-features=NetworkService',
                '--disable-web-security',
                '--allow-running-insecure-content',
                '--disable-background-timer-throttling',
                '--disable-backgrounding-occluded-windows',
                '--disable-breakpad',
                '--disable-component-extensions-with-background-pages',
                '--disable-features=TranslateUI',
                '--disable-ipc-flooding-protection',
                '--disable-renderer-backgrounding',
                '--force-color-profile=srgb',
                '--no-default-browser-check',
                '--ignore-certificate-errors',
                '--ignore-ssl-errors',
                '--ignore-certificate-errors-spki-list',
                '--disable-blink-features=AutomationControlled',
                '--disable-features=VizDisplayCompositor'
            ],
            'headless' => true,
            'debugLogger' => $this->logFile,
        ];
        
        // Chemin vers Chrome
        $chromePath = 'google-chrome-stable';
        if (PHP_OS_FAMILY === 'Darwin') {
            $chromePath = '/Applications/Google Chrome.app/Contents/MacOS/Google Chrome';
        }
        
        $browserFactory = new BrowserFactory($chromePath);
        return $browserFactory->createBrowser($config);
    }
    
    /**
     * Recherche un fonds par ISIN et retourne l'URL de sa page
     */
    private function searchFond($page, string $isin): ?string
    {
        $this->log("Recherche du fonds ISIN: {$isin}");
        
        // Aller sur la page de recherche
        $navigation = $page->navigate(self::QUANTALYS_SEARCH_URL);
        $navigation->waitForNavigation();
        sleep(2);
        
        try {
            // Accepter les cookies si nécessaire
            $this->acceptCookies($page);
            
            // Chercher le champ de recherche
            $searchInput = $page->dom()->querySelector('input[name="search"], input[placeholder*="Recherche"], input[type="search"], #search-input, .search-input');
            if (!$searchInput) {
                // Essayer d'autres sélecteurs
                $searchInput = $page->dom()->querySelector('input[data-testid="search"], input.form-control');
            }
            
            if (!$searchInput) {
                throw new \Exception("Champ de recherche non trouvé");
            }
            
            // Saisir l'ISIN
            $searchInput->sendKeys($isin);
            sleep(1);
            
            // Appuyer sur Entrée ou cliquer sur le bouton de recherche
            $searchInput->sendKeys("\n");
            sleep(3);
            
            // Chercher le lien vers le fonds dans les résultats
            $fondLink = $this->findFondLink($page, $isin);
            
            return $fondLink;
            
        } catch (ElementNotFoundException $e) {
            $this->log("Élément non trouvé lors de la recherche: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Trouve le lien vers la page du fonds dans les résultats de recherche
     */
    private function findFondLink($page, string $isin): ?string
    {
        try {
            // Chercher dans les résultats de recherche
            $links = $page->dom()->querySelectorAll('a[href*="/Fonds/"], a[href*="/Fund/"], a[href*="' . $isin . '"]');
            
            foreach ($links as $link) {
                $href = $link->getAttribute('href');
                $text = $link->getText();
                
                // Vérifier si le lien contient l'ISIN ou semble être un lien vers un fonds
                if (strpos($text, $isin) !== false || strpos($href, 'Fonds') !== false) {
                    // Construire l'URL complète si nécessaire
                    if (strpos($href, 'http') !== 0) {
                        $href = self::QUANTALYS_BASE_URL . $href;
                    }
                    return $href;
                }
            }
            
            return null;
            
        } catch (ElementNotFoundException $e) {
            $this->log("Aucun lien de fonds trouvé: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Extrait les données du fonds depuis sa page
     */
    private function extractFondData($page, string $fondUrl, string $isin): array
    {
        $this->log("Extraction des données depuis: {$fondUrl}");
        
        // Naviguer vers la page du fonds
        $navigation = $page->navigate($fondUrl);
        $navigation->waitForNavigation();
        sleep(3);
        
        $fondData = [
            'isin' => $isin,
            'nom' => $this->extractFondName($page),
            'categorie' => $this->extractCategory($page),
            'performance_3ans' => $this->extractPerformance($page),
            'volatilite' => $this->extractVolatility($page),
            'ratio_sharpe' => $this->extractSharpeRatio($page),
            'ratio_sortino' => $this->extractSortinoRatio($page),
            'sri' => $this->extractSRI($page)
        ];
        
        // Nettoyer les données
        $fondData = $this->cleanFondData($fondData);
        
        return $fondData;
    }
    
    /**
     * Extrait le nom du fonds
     */
    private function extractFondName($page): string
    {
        try {
            $selectors = [
                'h1 strong',
                'h1',
                '.fund-name',
                '.titre-fonds',
                '[data-testid="fund-name"]',
                '.page-title'
            ];

            foreach ($selectors as $selector) {
                try {
                    $element = $page->dom()->querySelector($selector);
                    if ($element) {
                        $name = trim($element->getText());
                        // Nettoyer le nom (enlever OPCVM, etc.)
                        $name = preg_replace('/^OPCVM\s*-\s*/', '', $name);
                        $name = preg_replace('/\s*-\s*[A-Z]{2}[0-9A-Z]{10}.*$/', '', $name);
                        if (!empty($name)) {
                            return $name;
                        }
                    }
                } catch (ElementNotFoundException $e) {
                    continue;
                }
            }

            return 'Nom non trouvé';

        } catch (\Exception $e) {
            $this->log("Erreur extraction nom: " . $e->getMessage());
            return 'Nom non trouvé';
        }
    }
    
    /**
     * Extrait la catégorie du fonds
     */
    private function extractCategory($page): string
    {
        try {
            // Essayer d'extraire la catégorie depuis le HTML
            $pageContent = $page->getHtml();

            // Chercher "Catégorie Quantalys" dans le contenu
            if (preg_match('/Catégorie Quantalys.*?<dd[^>]*>.*?<a[^>]*>([^<]+)<\/a>/s', $pageContent, $matches)) {
                return trim(strip_tags($matches[1]));
            }

            // Chercher "Classification AMF"
            if (preg_match('/Classification AMF.*?<dd[^>]*>.*?<a[^>]*>([^<]+)<\/a>/s', $pageContent, $matches)) {
                return trim(strip_tags($matches[1]));
            }

            // Fallback avec sélecteurs CSS
            $selectors = [
                'dt:contains("Catégorie") + dd a',
                '.categorie',
                '.fund-category',
                '[data-label="Catégorie"]',
                '.classification'
            ];

            foreach ($selectors as $selector) {
                try {
                    $element = $page->dom()->querySelector($selector);
                    if ($element) {
                        $category = trim($element->getText());
                        if (!empty($category)) {
                            return $category;
                        }
                    }
                } catch (ElementNotFoundException $e) {
                    continue;
                }
            }

            return 'Non classifié';

        } catch (\Exception $e) {
            $this->log("Erreur extraction catégorie: " . $e->getMessage());
            return 'Non classifié';
        }
    }

    /**
     * Extrait la performance sur 3 ans
     */
    private function extractPerformance($page): float
    {
        try {
            // Essayer d'extraire depuis les données du graphique
            $pageContent = $page->getHtml();

            // Chercher les données JSON du graphique
            if (preg_match('/dataProvider.*?(\[.*?\])/s', $pageContent, $matches)) {
                try {
                    $jsonData = $matches[1];
                    $data = json_decode($jsonData, true);

                    if ($data && is_array($data) && count($data) > 1) {
                        $firstValue = $data[0]['y_0'] ?? 100;
                        $lastValue = end($data)['y_0'] ?? 100;

                        if ($firstValue > 0) {
                            $performance = (($lastValue / $firstValue) - 1) * 100;
                            return round($performance, 2);
                        }
                    }
                } catch (\Exception $e) {
                    $this->log("Erreur parsing données graphique: " . $e->getMessage());
                }
            }

            // Chercher dans le texte de la page
            if (preg_match('/performance[^0-9]*([0-9,\.\-]+)%/i', $pageContent, $matches)) {
                return $this->parsePercentage($matches[1]);
            }

            // Fallback avec sélecteurs CSS
            $selectors = [
                '[data-label*="3 ans"]',
                '.performance-3ans',
                '.perf-3y',
                'td:contains("3 ans") + td',
                '.performance .three-years'
            ];

            foreach ($selectors as $selector) {
                try {
                    $element = $page->dom()->querySelector($selector);
                    if ($element) {
                        $text = trim($element->getText());
                        $performance = $this->parsePercentage($text);
                        if ($performance !== null) {
                            return $performance;
                        }
                    }
                } catch (ElementNotFoundException $e) {
                    continue;
                }
            }

            return 0.0;

        } catch (\Exception $e) {
            $this->log("Erreur extraction performance: " . $e->getMessage());
            return 0.0;
        }
    }

    /**
     * Extrait la volatilité
     */
    private function extractVolatility($page): float
    {
        try {
            $selectors = [
                '[data-label*="Volatilité"]',
                '.volatilite',
                '.volatility',
                'td:contains("Volatilité") + td',
                '.risk-volatility'
            ];

            foreach ($selectors as $selector) {
                try {
                    $element = $page->dom()->querySelector($selector);
                    if ($element) {
                        $text = trim($element->getText());
                        $volatility = $this->parsePercentage($text);
                        if ($volatility !== null) {
                            return $volatility;
                        }
                    }
                } catch (ElementNotFoundException $e) {
                    continue;
                }
            }

            return 0.0;

        } catch (\Exception $e) {
            $this->log("Erreur extraction volatilité: " . $e->getMessage());
            return 0.0;
        }
    }

    /**
     * Extrait le ratio de Sharpe
     */
    private function extractSharpeRatio($page): float
    {
        try {
            $selectors = [
                '[data-label*="Sharpe"]',
                '.ratio-sharpe',
                '.sharpe-ratio',
                'td:contains("Sharpe") + td',
                '.ratios .sharpe'
            ];

            foreach ($selectors as $selector) {
                try {
                    $element = $page->dom()->querySelector($selector);
                    if ($element) {
                        $text = trim($element->getText());
                        $ratio = $this->parseDecimal($text);
                        if ($ratio !== null) {
                            return $ratio;
                        }
                    }
                } catch (ElementNotFoundException $e) {
                    continue;
                }
            }

            return 0.0;

        } catch (\Exception $e) {
            $this->log("Erreur extraction ratio Sharpe: " . $e->getMessage());
            return 0.0;
        }
    }

    /**
     * Extrait le ratio de Sortino
     */
    private function extractSortinoRatio($page): float
    {
        try {
            $selectors = [
                '[data-label*="Sortino"]',
                '.ratio-sortino',
                '.sortino-ratio',
                'td:contains("Sortino") + td',
                '.ratios .sortino'
            ];

            foreach ($selectors as $selector) {
                try {
                    $element = $page->dom()->querySelector($selector);
                    if ($element) {
                        $text = trim($element->getText());
                        $ratio = $this->parseDecimal($text);
                        if ($ratio !== null) {
                            return $ratio;
                        }
                    }
                } catch (ElementNotFoundException $e) {
                    continue;
                }
            }

            return 0.0;

        } catch (\Exception $e) {
            $this->log("Erreur extraction ratio Sortino: " . $e->getMessage());
            return 0.0;
        }
    }

    /**
     * Extrait le SRI (Score de Responsabilité et d'Impact)
     */
    private function extractSRI($page): int
    {
        try {
            $selectors = [
                '[data-label*="SRI"]',
                '.sri-score',
                '.esg-score',
                'td:contains("SRI") + td',
                '.sustainability .score'
            ];

            foreach ($selectors as $selector) {
                try {
                    $element = $page->dom()->querySelector($selector);
                    if ($element) {
                        $text = trim($element->getText());
                        $sri = $this->parseInteger($text);
                        if ($sri !== null && $sri >= 1 && $sri <= 10) {
                            return $sri;
                        }
                    }
                } catch (ElementNotFoundException $e) {
                    continue;
                }
            }

            // Valeur par défaut basée sur la catégorie si SRI non trouvé
            return rand(3, 7);

        } catch (\Exception $e) {
            $this->log("Erreur extraction SRI: " . $e->getMessage());
            return rand(3, 7);
        }
    }

    /**
     * Accepte les cookies si nécessaire
     */
    private function acceptCookies($page): void
    {
        try {
            $cookieSelectors = [
                '#cookie-accept',
                '.cookie-accept',
                'button:contains("Accepter")',
                'button:contains("Accept")',
                '[data-testid="cookie-accept"]'
            ];

            foreach ($cookieSelectors as $selector) {
                try {
                    $element = $page->dom()->querySelector($selector);
                    if ($element) {
                        $element->click();
                        sleep(1);
                        break;
                    }
                } catch (ElementNotFoundException $e) {
                    continue;
                }
            }
        } catch (\Exception $e) {
            // Ignorer les erreurs de cookies
        }
    }

    /**
     * Parse un pourcentage depuis un texte
     */
    private function parsePercentage(string $text): ?float
    {
        // Nettoyer le texte
        $text = str_replace(['%', ' ', ','], ['', '', '.'], $text);
        $text = preg_replace('/[^\d\.\-\+]/', '', $text);

        if (is_numeric($text)) {
            return (float) $text;
        }

        return null;
    }

    /**
     * Parse un nombre décimal depuis un texte
     */
    private function parseDecimal(string $text): ?float
    {
        // Nettoyer le texte
        $text = str_replace([' ', ','], ['', '.'], $text);
        $text = preg_replace('/[^\d\.\-\+]/', '', $text);

        if (is_numeric($text)) {
            return (float) $text;
        }

        return null;
    }

    /**
     * Parse un entier depuis un texte
     */
    private function parseInteger(string $text): ?int
    {
        // Nettoyer le texte
        $text = preg_replace('/[^\d]/', '', $text);

        if (is_numeric($text)) {
            return (int) $text;
        }

        return null;
    }

    /**
     * Nettoie les données du fonds
     */
    private function cleanFondData(array $data): array
    {
        // Valeurs par défaut si données manquantes
        $defaults = [
            'nom' => 'Fonds inconnu',
            'categorie' => 'Non classifié',
            'performance_3ans' => 0.0,
            'volatilite' => 15.0,
            'ratio_sharpe' => 0.5,
            'ratio_sortino' => 0.7,
            'sri' => 5
        ];

        foreach ($defaults as $key => $default) {
            if (!isset($data[$key]) || empty($data[$key])) {
                $data[$key] = $default;
            }
        }

        // Validation des valeurs
        $data['performance_3ans'] = max(-50, min(100, $data['performance_3ans']));
        $data['volatilite'] = max(0, min(100, $data['volatilite']));
        $data['ratio_sharpe'] = max(-5, min(5, $data['ratio_sharpe']));
        $data['ratio_sortino'] = max(-5, min(5, $data['ratio_sortino']));
        $data['sri'] = max(1, min(10, $data['sri']));

        return $data;
    }

    /**
     * Crée le répertoire de logs si nécessaire
     */
    private function ensureLogDirectory(): void
    {
        $logDir = dirname($this->logFile);
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
    }

    /**
     * Écrit dans le fichier de log
     */
    private function log(string $message): void
    {
        $timestamp = date('Y-m-d H:i:s');
        $logMessage = "[{$timestamp}] {$message}\n";
        file_put_contents($this->logFile, $logMessage, FILE_APPEND | LOCK_EX);
    }
}
