<?php

namespace Wallet;

use HeadlessChromium\BrowserFactory;
use HeadlessChromium\Exception\DomException;
use HeadlessChromium\Exception\ElementNotFoundException;

/**
 * Class QuantalysRobotService
 * Robot pour récupérer automatiquement les données des fonds depuis Quantalys
 */
class QuantalysRobotService
{
    const QUANTALYS_BASE_URL = 'https://www.quantalys.com';
    const QUANTALYS_SEARCH_URL = 'https://www.quantalys.com/Fonds';
    
    private $logFile;
    
    public function __construct()
    {
        $this->logFile = __DIR__ . '/../logs/quantalys-robot.log';
        $this->ensureLogDirectory();
    }
    
    /**
     * Récupère les données d'un fonds par son code ISIN
     */
    public function getFondDataByISIN(string $isin): array
    {
        $this->log("Début récupération pour ISIN: {$isin}");
        
        try {
            $browser = $this->createBrowser();
            $page = $browser->createPage();
            
            // Étape 1: Rechercher le fonds
            $fondUrl = $this->searchFond($page, $isin);
            if (!$fondUrl) {
                $this->log("Fonds non trouvé pour ISIN: {$isin}");
                return ['error' => "Fonds non trouvé pour l'ISIN: {$isin}"];
            }
            
            // Étape 2: Accéder à la page du fonds
            $fondData = $this->extractFondData($page, $fondUrl, $isin);
            
            $browser->close();
            
            $this->log("Données récupérées avec succès pour ISIN: {$isin}");
            return $fondData;
            
        } catch (\Exception $e) {
            $this->log("Erreur pour ISIN {$isin}: " . $e->getMessage());
            return ['error' => $e->getMessage()];
        }
    }
    
    /**
     * Récupère les données de plusieurs fonds
     */
    public function getMultipleFondsData(array $isins, int $maxConcurrent = 3): array
    {
        $results = [];
        $chunks = array_chunk($isins, $maxConcurrent);
        
        foreach ($chunks as $chunk) {
            foreach ($chunk as $isin) {
                $results[$isin] = $this->getFondDataByISIN($isin);
                
                // Pause entre les requêtes pour éviter la détection
                sleep(rand(2, 5));
            }
            
            // Pause plus longue entre les chunks
            if (count($chunks) > 1) {
                sleep(rand(10, 20));
            }
        }
        
        return $results;
    }
    
    /**
     * Crée une instance de navigateur Chrome
     */
    private function createBrowser()
    {
        $config = [
            'userAgent' => 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'noSandbox' => true,
            'keepAlive' => false,
            'connectionTimeout' => 60000,
            'sendSyncDefaultTimeout' => 60000,
            'ignoreCertificateErrors' => true,
            'arguments' => [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-gpu',
                '--disable-software-rasterizer',
                '--disable-crash-reporter',
                '--disable-crashpad',
                '--crash-dumps-dir=/tmp',
                '--disable-background-networking',
                '--disable-default-apps',
                '--disable-extensions',
                '--disable-sync',
                '--disable-translate',
                '--hide-scrollbars',
                '--metrics-recording-only',
                '--mute-audio',
                '--no-first-run',
                '--no-zygote',
                '--single-process',
                '--disable-features=site-per-process',
                '--disable-features=NetworkService',
                '--disable-web-security',
                '--allow-running-insecure-content',
                '--disable-background-timer-throttling',
                '--disable-backgrounding-occluded-windows',
                '--disable-breakpad',
                '--disable-component-extensions-with-background-pages',
                '--disable-features=TranslateUI',
                '--disable-ipc-flooding-protection',
                '--disable-renderer-backgrounding',
                '--force-color-profile=srgb',
                '--no-default-browser-check',
                '--ignore-certificate-errors',
                '--ignore-ssl-errors',
                '--ignore-certificate-errors-spki-list',
                '--disable-blink-features=AutomationControlled',
                '--disable-features=VizDisplayCompositor'
            ],
            'headless' => true,
            'debugLogger' => $this->logFile,
        ];
        
        // Chemin vers Chrome
        $chromePath = 'google-chrome-stable';
        if (PHP_OS_FAMILY === 'Darwin') {
            $chromePath = '/Applications/Google Chrome.app/Contents/MacOS/Google Chrome';
        }
        
        $browserFactory = new BrowserFactory($chromePath);
        return $browserFactory->createBrowser($config);
    }
    
    /**
     * Recherche un fonds par ISIN et retourne l'URL de sa page
     */
    private function searchFond($page, string $isin): ?string
    {
        $this->log("Recherche du fonds ISIN: {$isin}");
        
        // Aller sur la page de recherche
        $navigation = $page->navigate(self::QUANTALYS_SEARCH_URL);
        $navigation->waitForNavigation();
        sleep(2);
        
        try {
            // Accepter les cookies si nécessaire
            $this->acceptCookies($page);
            
            // Chercher le champ de recherche
            $searchInput = $page->dom()->querySelector('input[name="search"], input[placeholder*="Recherche"], input[type="search"], #search-input, .search-input');
            if (!$searchInput) {
                // Essayer d'autres sélecteurs
                $searchInput = $page->dom()->querySelector('input[data-testid="search"], input.form-control');
            }
            
            if (!$searchInput) {
                throw new \Exception("Champ de recherche non trouvé");
            }
            
            // Saisir l'ISIN
            $searchInput->sendKeys($isin);
            sleep(1);
            
            // Appuyer sur Entrée ou cliquer sur le bouton de recherche
            $searchInput->sendKeys("\n");
            sleep(3);
            
            // Chercher le lien vers le fonds dans les résultats
            $fondLink = $this->findFondLink($page, $isin);
            
            return $fondLink;
            
        } catch (ElementNotFoundException $e) {
            $this->log("Élément non trouvé lors de la recherche: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Trouve le lien vers la page du fonds dans les résultats de recherche
     */
    private function findFondLink($page, string $isin): ?string
    {
        try {
            // Chercher dans les résultats de recherche
            $links = $page->dom()->querySelectorAll('a[href*="/Fonds/"], a[href*="/Fund/"], a[href*="' . $isin . '"]');
            
            foreach ($links as $link) {
                $href = $link->getAttribute('href');
                $text = $link->getText();
                
                // Vérifier si le lien contient l'ISIN ou semble être un lien vers un fonds
                if (strpos($text, $isin) !== false || strpos($href, 'Fonds') !== false) {
                    // Construire l'URL complète si nécessaire
                    if (strpos($href, 'http') !== 0) {
                        $href = self::QUANTALYS_BASE_URL . $href;
                    }
                    return $href;
                }
            }
            
            return null;
            
        } catch (ElementNotFoundException $e) {
            $this->log("Aucun lien de fonds trouvé: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Extrait les données du fonds depuis sa page
     */
    private function extractFondData($page, string $fondUrl, string $isin): array
    {
        $this->log("Extraction des données depuis: {$fondUrl}");
        
        // Naviguer vers la page du fonds
        $navigation = $page->navigate($fondUrl);
        $navigation->waitForNavigation();
        sleep(3);
        
        $fondData = [
            'isin' => $isin,
            'nom' => $this->extractFondName($page),
            'categorie' => $this->extractCategory($page),
            'performance_3ans' => $this->extractPerformance($page),
            'volatilite' => $this->extractVolatility($page),
            'ratio_sharpe' => $this->extractSharpeRatio($page),
            'ratio_sortino' => $this->extractSortinoRatio($page),
            'sri' => $this->extractSRI($page)
        ];
        
        // Nettoyer les données
        $fondData = $this->cleanFondData($fondData);
        
        return $fondData;
    }
    
    /**
     * Extrait le nom du fonds
     */
    private function extractFondName($page): string
    {
        try {
            $selectors = [
                'h1',
                '.fund-name',
                '.titre-fonds',
                '[data-testid="fund-name"]',
                '.page-title'
            ];
            
            foreach ($selectors as $selector) {
                try {
                    $element = $page->dom()->querySelector($selector);
                    if ($element) {
                        $name = trim($element->getText());
                        if (!empty($name)) {
                            return $name;
                        }
                    }
                } catch (ElementNotFoundException $e) {
                    continue;
                }
            }
            
            return 'Nom non trouvé';
            
        } catch (\Exception $e) {
            $this->log("Erreur extraction nom: " . $e->getMessage());
            return 'Nom non trouvé';
        }
    }
    
    /**
     * Extrait la catégorie du fonds
     */
    private function extractCategory($page): string
    {
        try {
            $selectors = [
                '.categorie',
                '.fund-category',
                '[data-label="Catégorie"]',
                '.classification'
            ];
            
            foreach ($selectors as $selector) {
                try {
                    $element = $page->dom()->querySelector($selector);
                    if ($element) {
                        $category = trim($element->getText());
                        if (!empty($category)) {
                            return $category;
                        }
                    }
                } catch (ElementNotFoundException $e) {
                    continue;
                }
            }
            
            return 'Non classifié';
            
        } catch (\Exception $e) {
            $this->log("Erreur extraction catégorie: " . $e->getMessage());
            return 'Non classifié';
        }
    }
