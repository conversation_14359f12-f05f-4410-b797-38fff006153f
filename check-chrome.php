<?php
echo "🔍 Vérification de l'installation de Chrome\n";
echo "==========================================\n\n";

// Chemins possibles pour Chrome
$chromePaths = [
    'google-chrome-stable',
    'google-chrome',
    'chromium-browser',
    'chromium',
    '/usr/bin/google-chrome-stable',
    '/usr/bin/google-chrome',
    '/usr/bin/chromium-browser',
    '/usr/bin/chromium',
    '/Applications/Google Chrome.app/Contents/MacOS/Google Chrome', // macOS
    '/opt/google/chrome/chrome'
];

$foundChrome = false;
$chromePath = null;

echo "🔎 Recherche de Chrome...\n";

foreach ($chromePaths as $path) {
    echo "   Vérification: {$path} ";
    
    if (file_exists($path)) {
        echo "✅ Trouvé\n";
        $foundChrome = true;
        $chromePath = $path;
        break;
    } else {
        // Essayer avec which/where
        $output = [];
        $returnCode = 0;
        exec("which {$path} 2>/dev/null", $output, $returnCode);
        
        if ($returnCode === 0 && !empty($output)) {
            echo "✅ Trouvé via which: " . $output[0] . "\n";
            $foundChrome = true;
            $chromePath = $output[0];
            break;
        } else {
            echo "❌ Non trouvé\n";
        }
    }
}

echo "\n";

if ($foundChrome) {
    echo "🎉 Chrome trouvé: {$chromePath}\n\n";
    
    // Tester la version
    echo "📋 Informations sur Chrome:\n";
    $output = [];
    exec("{$chromePath} --version 2>/dev/null", $output);
    if (!empty($output)) {
        echo "   Version: " . $output[0] . "\n";
    }
    
    // Tester les arguments de base
    echo "\n🧪 Test de lancement basique...\n";
    $testCommand = "{$chromePath} --headless --disable-gpu --no-sandbox --dump-dom about:blank 2>/dev/null";
    $output = [];
    $returnCode = 0;
    exec($testCommand, $output, $returnCode);
    
    if ($returnCode === 0) {
        echo "✅ Chrome peut être lancé en mode headless\n";
    } else {
        echo "❌ Erreur lors du lancement de Chrome (code: {$returnCode})\n";
    }
    
} else {
    echo "❌ Chrome non trouvé !\n\n";
    echo "📥 Instructions d'installation:\n\n";
    
    if (PHP_OS_FAMILY === 'Linux') {
        echo "Ubuntu/Debian:\n";
        echo "   wget -q -O - https://dl.google.com/linux/linux_signing_key.pub | sudo apt-key add -\n";
        echo "   sudo sh -c 'echo \"deb [arch=amd64] http://dl.google.com/linux/chrome/deb/ stable main\" >> /etc/apt/sources.list.d/google-chrome.list'\n";
        echo "   sudo apt update\n";
        echo "   sudo apt install google-chrome-stable\n\n";
        
        echo "CentOS/RHEL/Fedora:\n";
        echo "   sudo dnf install google-chrome-stable\n";
        echo "   # ou\n";
        echo "   sudo yum install google-chrome-stable\n\n";
        
    } elseif (PHP_OS_FAMILY === 'Darwin') {
        echo "macOS:\n";
        echo "   1. Téléchargez Chrome depuis https://www.google.com/chrome/\n";
        echo "   2. Ou utilisez Homebrew: brew install --cask google-chrome\n\n";
        
    } elseif (PHP_OS_FAMILY === 'Windows') {
        echo "Windows:\n";
        echo "   1. Téléchargez Chrome depuis https://www.google.com/chrome/\n";
        echo "   2. Installez normalement\n\n";
    }
}

// Vérifier les dépendances PHP
echo "🔧 Vérification des dépendances PHP:\n";

$requiredExtensions = ['curl', 'json', 'pdo'];
foreach ($requiredExtensions as $ext) {
    if (extension_loaded($ext)) {
        echo "   ✅ Extension {$ext}: Installée\n";
    } else {
        echo "   ❌ Extension {$ext}: Manquante\n";
    }
}

// Vérifier Composer
echo "\n📦 Vérification de Composer:\n";
if (file_exists(__DIR__ . '/vendor/autoload.php')) {
    echo "   ✅ Autoloader Composer: Présent\n";
} else {
    echo "   ❌ Autoloader Composer: Manquant (lancez 'composer install')\n";
}

// Vérifier chrome-php
if (class_exists('HeadlessChromium\\BrowserFactory')) {
    echo "   ✅ Librairie chrome-php: Installée\n";
} else {
    echo "   ❌ Librairie chrome-php: Manquante\n";
}

echo "\n";

if ($foundChrome && extension_loaded('curl') && class_exists('HeadlessChromium\\BrowserFactory')) {
    echo "🎉 Tout est prêt pour utiliser le robot Quantalys !\n";
    echo "Vous pouvez maintenant lancer:\n";
    echo "   php test-quantalys-robot.php\n";
} else {
    echo "⚠️  Certains éléments sont manquants. Installez-les avant d'utiliser le robot.\n";
}
?>
