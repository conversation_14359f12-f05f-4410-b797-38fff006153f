<?php
require_once 'config/config.php';
require_once 'vendor/autoload.php';

use Wallet\FondsManager;

echo "📊 Ajout de données d'exemple supplémentaires\n";
echo "============================================\n\n";

// Codes ISIN supplémentaires pour enrichir les tests
$additionalCodes = [
    'IE00B4L5Y983', // iShares Core MSCI World
    'FR0010527275', // Amundi MSCI Emerging Markets
    'FR0010688440', // Lyxor CAC 40
];

try {
    $fondsManager = new FondsManager();
    
    echo "Synchronisation des fonds supplémentaires...\n";
    $results = $fondsManager->syncFonds($additionalCodes);
    
    $synced = 0;
    foreach ($results as $result) {
        if (!isset($result['error'])) {
            $synced++;
            echo "✅ Fond ajouté: " . $result['nom'] . "\n";
        } else {
            echo "⚠️  " . $result['error'] . "\n";
        }
    }
    
    echo "\n📈 Récapitulatif:\n";
    echo "- Fonds synchronisés: {$synced}\n";
    
    // Afficher tous les fonds en base
    $allFonds = $fondsManager->getAllFonds();
    echo "- Total fonds en base: " . count($allFonds) . "\n\n";
    
    echo "🎯 Fonds disponibles:\n";
    foreach ($allFonds as $fond) {
        echo "   • " . $fond['code_isin'] . " - " . $fond['nom'] . " (Note: " . $fond['note'] . "/10)\n";
    }
    
    echo "\n🎉 Données d'exemple ajoutées avec succès !\n";
    echo "Vous pouvez maintenant tester l'application avec plus de fonds.\n";
    
} catch (Exception $e) {
    echo "❌ Erreur: " . $e->getMessage() . "\n";
}
?>
