<?php
require_once 'config/config.php';
require_once 'vendor/autoload.php';

use Wallet\Database;

echo "🔄 Génération des données simulées pour TOUS les fonds Floriane 2\n";
echo "================================================================\n\n";

// Liste COMPLÈTE des codes ISIN du contrat Floriane 2
$allFloriane2Codes = [
    'FR001400STS3', 'FR0012287316', 'FR0012829364', 'FR0013048287', 'FR0013118692',
    'FR0013184991', 'FR0013218625', 'FR0013242161', 'FR0013261245', 'FR0012297240',
    'FR0013282514', 'FR0013216207', 'FR0010176891', 'FR0010188383', 'FR0013436037',
    'FR0010599373', 'FR0010458745', 'FR0010251736', 'FR0000991432', 'FR0010716332',
    'FR0000972655', 'FR0013436045', 'FR0010844365', 'FR0010750869', 'FR0000944712',
    'FR0000944696', 'FR0012336683', 'FR0011556828', 'FR0010478768', 'FR0010165944',
    'FR0010153320', 'FR0010698555', 'LU1681047079', 'FR0013380607', 'FR001400ZGQ9',
    'FR0013379336', 'FR0013436052', 'FR0013487170', 'FR0010829697', 'FR0011199371',
    'LU2356220926', 'FR0011630557', 'FR0011176635', 'FR0011176627', 'FR0011408798',
    'FR0011408764', 'FR0007054358', 'FR001400ZGP1', 'LU1681047319', 'LU1681047400',
    'FR0010093682', 'FR0011223577', 'LU0557854147', 'LU0119085271', 'LU0119085867',
    'LU0568620560', 'LU0552028184', 'LU0613075240', 'LU0907913460', 'LU0557858130',
    'LU0347592197', 'LU0568583420', 'LU0616241476', 'LU0119099819', 'LU0119100179',
    'LU0119110723', 'LU0119110996', 'LU0907331507', 'LU0201576401', 'LU1883303635',
    'LU0568607203', 'LU0755949848', 'LU0557861274', 'LU0906524193', 'LU0557863056',
    'LU1883342377', 'LU1883318740', 'LU0119133188', 'LU0119133691', 'LU0442405998',
    'LU0557866588', 'LU0248702192', 'LU0552029406', 'LU0119108826', 'LU0119109048',
    'LU1941681956', 'LU0552029232', 'LU1883872332', 'LU0272941971', 'LU0557872479',
    'LU1563454310', 'FR0011358092', 'FR0013441698', 'FR0014003P18', 'FR0010721407',
    'LU1437017350', 'FR0010750877', 'LU1681037609', 'FR001400SDI8', 'FR001400SDM0',
    'FR001400SDN8', 'LU1900068161', 'FR0011720911', 'LU1861138961', 'LU2109787635',
    'LU1861137484', 'FR0010361683', 'LU2233156749', 'FR0014002CG3', 'LU1602145036',
    'LU1861132840', 'FR0014002CH1', 'FR0014003IY1', 'LU1681043599', 'FR0010433391',
    'FR0000983637', 'FR0013436060', 'LU1829221024', 'FR0010093724', 'FR0010107169',
    'FR0010156604', 'FR0000286338', 'FR0013436078', 'FR0011882364', 'FR001400ZGO4',
    'FR001400S9V0', 'FR001400ZGR7', 'FR001400ZGS5', 'FR0011871128', 'FR0010697466',
    'FR0007481536', 'FR0010820332', 'FR0013411741', 'FR0012635654', 'LU1135865084',
    'LU1681048804', 'LU2195226068', 'FR0000288185', 'FR0013436110', 'FR0010106880',
    'FR0010101972', 'FR0010106856', 'FR0010093716', 'FR0010106864', 'FR0011884048',
    'FR0012099455', 'FR0011884055', 'FR0013436011', 'FR0000991424', 'FR0000973802',
    'FR0013436128', 'FR00140036V1', 'FR0010036962', 'FR0010230474', 'FR0010536565',
    'FR0011660877', 'FR0012903250', 'FR001400C9Z9', 'FR0010638676', 'FR0011528876',
    'FR00140039G6', 'FR0010340612', 'FR0013438025', 'FR001400D3W8', 'FR001400MML6',
    'FR001400P9A7', 'FR001400NK21', 'FR001400R948', 'FR0013245230', 'FR0013233087',
    'FR0014009YE7', 'FR0013254505', 'FR001400BXZ6', 'FR0013274743', 'FR0013196995',
    'FR001400HJA5', 'FR001400KZE7', 'FR001400JO56', 'FR001400F0A8', 'FR001400FSO1',
    'FR001400EKK9', 'FR001400MQS2', 'FR001400HI80', 'FR001400PFF5', 'FR001400NFA1',
    'FR001400L180', 'FR001400J4F5', 'FR0013133196', 'FR0013030137', 'FR0013217817',
    'FR0013293784', 'FR0013076346', 'FR0013224714', 'FR0012737997', 'FR0013166469',
    'FR0013257094', 'FR0013245578', 'FR0012892016', 'FR0013192903', 'FR0013275641',
    'FR0013133766', 'FR0013029246', 'FR0013217015', 'FR0013291465', 'FR0013072675',
    'FR0013227998', 'FR0013308392', 'FR0012737302', 'FR0013174679', 'FR0013254083',
    'FR0013241478', 'FR0012880201', 'FR0013196854', 'FR0013274669', 'FR001400EMY6',
    'FR001400MTN7', 'FR001400HH81', 'FR001400FX18', 'FR001400L0U8', 'FR001400K9U0',
    'FR0000439614', 'FR0013297926', 'FR0014000EF5', 'FR0014002184', 'FR0014003KT7',
    'FR0013321478', 'FR0013349602', 'FR0013380813', 'FR0013406444', 'FR0013425014',
    'FR0013458692', 'FR0013487683', 'FR0013518180', 'FR0010744532', 'FR0010619916',
    'FR0011354646', 'FR0010469312', 'FR0010501858', 'FR001400A7W2', 'FR001400RQY9',
    'FR0010560177', 'FR0010097667', 'FR0010097642', 'FR0010097683', 'FR0010376020',
    'FR0012300374', 'LU2860962559', 'LU2462251500', 'LU1902443420', 'LU2570611322',
    'LU1653748860', 'LU1989763773', 'LU2035461578', 'LU2389405080', 'LU2036821663',
    'FR0010304089', 'FR0010836163', 'FR001400UHV8', 'FR001400ZPH9', 'FR001400XIC0',
    'FR001400UKZ3', 'FR001400PA13', 'FR001400T2O9', 'FR001400RCP7', 'FR001400U8I5',
    'FR001400XU76', 'FR0014003OK8', 'FR0013430378', 'LU0565135745', 'LU0433182416',
    'FR0011711621', 'FR0013201688', 'FR0013201696', 'FR0013217643', 'FR0013241288',
    'FR0013493285', 'FR0014005JQ0', 'FR0013217650', 'FR0014004B13', 'FR0014004206',
    'FR0007447891', 'FR0011063353', 'FR001400DG60', 'FR001400LYO7', 'FR001400U0G6',
    'FR001400H5K4', 'FR001400O8Q6', 'FR001400III8', 'FR001400QFG1', 'FR0014006VK6',
    'FR001400E474', 'FR001400LYP4', 'FR0014009G88', 'FR001400H8T9', 'FR001400O754',
    'FR0014004QT2', 'FR001400ATA9', 'FR001400IIG2', 'FR001400QFF3', 'FR0014006FY0',
    'FR0014009AG2', 'FR001400ATB7', 'FR0010407247', 'FR0011161173', 'FR0011363738',
    'FR0011363712', 'FR0013428349'
];

// Catégories possibles pour les fonds
$categories = [
    'Actions françaises', 'Actions européennes', 'Actions internationales', 'Actions américaines',
    'Actions émergentes', 'Actions japonaises', 'Actions Pacifique', 'Technologies',
    'Santé', 'Énergies renouvelables', 'Consommation', 'Immobilier', 'Services financiers',
    'Obligations gouvernementales', 'Obligations d\'entreprise', 'Obligations vertes',
    'Obligations émergentes', 'Monétaire', 'Allocation prudente', 'Allocation équilibrée',
    'Allocation dynamique', 'Allocation diversifiée', 'Matières premières', 'Or',
    'Actions ESG', 'Obligations ESG', 'Fonds thématiques', 'Infrastructure'
];

// Noms génériques pour les fonds
$nomsPrefixes = [
    'Amundi', 'BNP Paribas', 'Crédit Agricole', 'Société Générale', 'AXA', 'Natixis',
    'BNPP', 'CA Indosuez', 'Predica', 'Lyxor', 'iShares', 'Xtrackers', 'Vanguard',
    'SPDR', 'Invesco', 'Franklin Templeton', 'Schroders', 'Fidelity', 'BlackRock'
];

$nomsSuffixes = [
    'UCITS ETF', 'Fund', 'Equity', 'Bond', 'Multi-Asset', 'ESG', 'SRI', 'Impact',
    'Growth', 'Value', 'Income', 'Dividend', 'Quality', 'Momentum', 'Low Vol'
];

try {
    $db = Database::getInstance();
    
    echo "Génération de données pour " . count($allFloriane2Codes) . " fonds...\n\n";
    
    $inserted = 0;
    $errors = 0;
    
    foreach ($allFloriane2Codes as $index => $isin) {
        try {
            // Vérifier si le fonds existe déjà
            $existing = $db->fetchOne("SELECT id FROM fonds WHERE code_isin = ?", [$isin]);
            if ($existing) {
                echo "⏭️  Fonds {$isin} déjà existant\n";
                continue;
            }
            
            // Générer des données réalistes
            $categorie = $categories[array_rand($categories)];
            $prefix = $nomsPrefixes[array_rand($nomsPrefixes)];
            $suffix = $nomsSuffixes[array_rand($nomsSuffixes)];
            $nom = "{$prefix} {$categorie} {$suffix}";
            
            // Performance : entre -5% et +20% avec distribution réaliste
            $performance = round(mt_rand(-500, 2000) / 100, 2);
            
            // Volatilité : entre 1% et 35% selon la catégorie
            $volatiliteBase = strpos($categorie, 'Monétaire') !== false ? 1 : 
                             (strpos($categorie, 'Obligations') !== false ? 5 : 15);
            $volatilite = round($volatiliteBase + mt_rand(0, 1500) / 100, 2);
            
            // Ratios de Sharpe et Sortino corrélés à la performance et volatilité
            $ratioSharpe = $volatilite > 0 ? round(($performance - 2) / $volatilite, 3) : 0;
            $ratioSortino = round($ratioSharpe * (1 + mt_rand(10, 30) / 100), 3);
            
            // SRI : plus élevé pour les fonds ESG
            $sriBase = (strpos($categorie, 'ESG') !== false || strpos($nom, 'ESG') !== false || 
                       strpos($nom, 'SRI') !== false || strpos($categorie, 'vert') !== false) ? 7 : 4;
            $sri = min(10, max(1, $sriBase + mt_rand(-2, 3)));
            
            // Calculer la note
            $notePerf = max(0, min(10, ($performance + 5) * 2));
            $noteSharpe = $ratioSharpe >= 0.7 ? 10 : ($ratioSharpe >= 0.5 ? 6 : 2);
            $noteSortino = $ratioSortino >= 0.7 ? 10 : ($ratioSortino >= 0.5 ? 6 : 2);
            $note = round(($notePerf * 0.4) + ($noteSharpe * 0.3) + ($noteSortino * 0.3), 1);
            
            // Insérer en base
            $data = [
                'code_isin' => $isin,
                'nom' => $nom,
                'categorie' => $categorie,
                'performance_3ans' => $performance,
                'volatilite' => $volatilite,
                'ratio_sharpe' => $ratioSharpe,
                'ratio_sortino' => $ratioSortino,
                'sri' => $sri,
                'note' => $note
            ];
            
            $db->insert('fonds', $data);
            $inserted++;
            
            if ($inserted % 50 == 0) {
                echo "✅ {$inserted} fonds traités...\n";
            }
            
        } catch (Exception $e) {
            $errors++;
            echo "❌ Erreur pour {$isin}: " . $e->getMessage() . "\n";
        }
    }
    
    echo "\n📊 RÉSULTATS:\n";
    echo "- Fonds insérés: {$inserted}\n";
    echo "- Erreurs: {$errors}\n";
    echo "- Total codes traités: " . count($allFloriane2Codes) . "\n\n";
    
    // Statistiques finales
    $totalFonds = $db->fetchOne("SELECT COUNT(*) as count FROM fonds")['count'];
    echo "🎯 Total fonds en base: {$totalFonds}\n";
    
    echo "\n🎉 Génération terminée ! Tous les fonds Floriane 2 sont maintenant disponibles.\n";
    echo "Accédez à http://localhost:8000 pour voir le résultat.\n";
    
} catch (Exception $e) {
    echo "❌ Erreur: " . $e->getMessage() . "\n";
}
?>
