<?php
require_once 'config/config.php';
require_once 'vendor/autoload.php';

use Wallet\QuantalysRobotServiceFinal;
use Wallet\Database;
use Wallet\Calculator;

echo "🤖 Scraping RÉEL des données Quantalys pour Floriane 2\n";
echo "======================================================\n\n";

// Liste complète des codes ISIN Floriane 2 (première partie pour test)
$floriane2Codes = [
    'FR0012287316',
    /*'FR0012287316', 'FR0012829364', 'FR0013048287', 'FR0013118692',
    'FR0013184991', 'FR0013218625', 'FR0013242161', 'FR0013261245', 'FR0012297240',
    'FR0013282514', 'FR0013216207', 'FR0010176891', 'FR0010188383', 'FR0013436037',
    'FR0010599373', 'FR0010458745', 'FR0010251736', 'FR0000991432', 'FR0010716332',
    'FR0000972655', 'FR0013436045', 'FR0010844365', 'FR0010750869', 'FR0000944712',
    'FR0000944696', 'FR0012336683', 'FR0011556828', 'FR0010478768', 'FR0010165944',
    'FR0010153320', 'FR0010698555', 'LU1681047079', 'FR0013380607', 'FR001400ZGQ9',
    'FR0013379336', 'FR0013436052', 'FR0013487170', 'FR0010829697', 'FR0011199371'*/
    // ... Ajoutez plus de codes selon vos besoins
];

// Configuration
$batchSize = 5; // Nombre de fonds à traiter par batch (réduit pour éviter la détection)
$pauseBetweenBatches = 60; // Pause en secondes entre les batches (augmentée)
$pauseBetweenRequests = 10; // Pause en secondes entre chaque requête (augmentée)

try {
    $robot = new QuantalysRobotServiceFinal();
    $db = Database::getInstance();

    echo "📊 Configuration du scraping:\n";
    echo "   • Total fonds à traiter: " . count($floriane2Codes) . "\n";
    echo "   • Taille des batches: {$batchSize}\n";
    echo "   • Pause entre batches: {$pauseBetweenBatches}s\n";
    echo "   • Pause entre requêtes: {$pauseBetweenRequests}s\n";
    echo "   • Temps estimé: " . round((count($floriane2Codes) * $pauseBetweenRequests) / 60, 1) . " minutes\n\n";

    $batches = array_chunk($floriane2Codes, $batchSize);
    $totalBatches = count($batches);
    $successCount = 0;
    $errorCount = 0;
    $skippedCount = 0;

    foreach ($batches as $batchIndex => $batch) {
        $currentBatch = $batchIndex + 1;
        echo "🔄 Batch {$currentBatch}/{$totalBatches} (" . count($batch) . " fonds)\n";
        echo str_repeat("=", 60) . "\n";

        foreach ($batch as $index => $isin) {
            $currentInBatch = $index + 1;
            $totalProcessed = ($batchIndex * $batchSize) + $currentInBatch;

            echo "[{$totalProcessed}/" . count($floriane2Codes) . "] ISIN: {$isin} ";

            try {
                // Vérifier si le fonds existe déjà avec des données réelles
                $existing = $db->fetchOne(
                    "SELECT * FROM fonds WHERE code_isin = ? AND nom NOT LIKE '%Fund%' AND nom NOT LIKE '%UCITS%' AND nom != 'Fonds inconnu'",
                    [$isin]
                );

                /*if ($existing && !empty($existing['nom']) && $existing['nom'] !== 'Nom non trouvé') {
                    echo "⏭️  Déjà traité (" . substr($existing['nom'], 0, 30) . "...)\n";
                    $skippedCount++;
                    continue;
                }*/

                // Récupérer les données via le robot
                $fondData = $robot->getFondDataByISIN($isin);
                var_dump($fondData);

                if (isset($fondData['error'])) {
                    echo "❌ " . $fondData['error'] . "\n";
                    $errorCount++;
                    continue;
                }

                // Vérifier que nous avons récupéré de vraies données
                if ($fondData['nom'] === 'Nom non trouvé' || $fondData['nom'] === 'Fonds inconnu') {
                    echo "⚠️  Données incomplètes\n";
                    $errorCount++;
                    continue;
                }

                // Calculer la note
                $note = Calculator::calculateNote($fondData);

                // Préparer les données pour la base
                $dataToSave = [
                    'code_isin' => $isin,
                    'nom' => $fondData['nom'],
                    'categorie' => $fondData['categorie'],
                    'performance_3ans' => $fondData['performance_3ans'],
                    'volatilite' => $fondData['volatilite'],
                    'ratio_sharpe' => $fondData['ratio_sharpe'],
                    'ratio_sortino' => $fondData['ratio_sortino'],
                    'sri' => $fondData['sri'],
                    'note' => $note
                ];

                // Sauvegarder en base
                if ($existing) {
                    $db->update('fonds', $dataToSave, 'code_isin = ?', [$isin]);
                } else {
                    $db->insert('fonds', $dataToSave);
                }

                echo "✅ " . substr($fondData['nom'], 0, 25) . "... (Note: {$note}/10, SRI: {$fondData['sri']})\n";
                $successCount++;

            } catch (Exception $e) {
                echo "❌ Erreur: " . $e->getMessage() . "\n";
                $errorCount++;
            }

            // Pause entre les requêtes
            if ($currentInBatch < count($batch)) {
                echo "   ⏳ Pause {$pauseBetweenRequests}s...\n";
                sleep($pauseBetweenRequests);
            }
        }

        echo "\n📈 Batch {$currentBatch} terminé:\n";
        echo "   • Succès: " . ($successCount - ($batchIndex * $batchSize > $successCount ? 0 : $batchIndex * $batchSize)) . "/" . count($batch) . "\n";
        echo "   • Total succès: {$successCount}\n";
        echo "   • Total erreurs: {$errorCount}\n";
        echo "   • Total ignorés: {$skippedCount}\n";

        // Pause entre les batches (sauf pour le dernier)
        if ($currentBatch < $totalBatches) {
            echo "\n⏳ Pause de {$pauseBetweenBatches} secondes avant le prochain batch...\n";
            echo "   (Temps pour éviter la détection par Quantalys)\n\n";
            sleep($pauseBetweenBatches);
        }
    }

    echo "\n🎉 SCRAPING TERMINÉ !\n";
    echo "====================\n";
    echo "✅ Succès: {$successCount}\n";
    echo "❌ Erreurs: {$errorCount}\n";
    echo "⏭️  Ignorés: {$skippedCount}\n";
    echo "📊 Total traité: " . ($successCount + $errorCount + $skippedCount) . "/" . count($floriane2Codes) . "\n";
    echo "📈 Taux de succès: " . round(($successCount / count($floriane2Codes)) * 100, 1) . "%\n\n";

    // Statistiques finales
    $totalFonds = $db->fetchOne("SELECT COUNT(*) as count FROM fonds WHERE nom != 'Fonds inconnu'")['count'];
    echo "📋 Statistiques finales:\n";
    echo "   • Total fonds réels en base: {$totalFonds}\n";

    $realFloriane2 = $db->fetchAll(
        "SELECT * FROM fonds WHERE code_isin IN ('" . implode("','", $floriane2Codes) . "') AND nom != 'Fonds inconnu' ORDER BY note DESC LIMIT 10"
    );

    echo "\n🏆 Top 10 des fonds Floriane 2 récupérés:\n";
    foreach ($realFloriane2 as $i => $fond) {
        $rank = $i + 1;
        echo "   {$rank}. " . substr($fond['nom'], 0, 40) . "... (Note: {$fond['note']}/10)\n";
    }

    echo "\n📋 Prochaines étapes:\n";
    echo "1. Vérifiez les logs dans logs/quantalys-robot.log\n";
    echo "2. Consultez l'application sur http://localhost:8000\n";
    echo "3. Relancez le script pour traiter plus de fonds si nécessaire\n";
    echo "4. Ajustez les pauses si vous rencontrez des blocages\n";

} catch (Exception $e) {
    echo "❌ Erreur critique: " . $e->getMessage() . "\n";
}
?>
