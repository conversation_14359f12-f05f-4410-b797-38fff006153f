<?php
require_once 'config/config.php';
require_once 'vendor/autoload.php';

use Wallet\FondsManager;

echo "📊 Ajout des fonds du contrat Floriane 2\n";
echo "========================================\n\n";

// Liste des fonds typiques du contrat Floriane 2 (Crédit Agricole/Predica)
// Basée sur les supports couramment disponibles dans ce type de contrat
$floriane2Codes = [
    // Fonds actions françaises
    'FR0000120073', // Air Liquide
    'FR0000131104', // BNP Paribas
    'FR0000120628', // AXA
    'FR0000125007', // Vivendi
    'FR0000120321', // L'Oréal
    
    // Fonds actions européennes
    'FR0010315770', // Amundi MSCI World UCITS ETF
    'IE00B4L5Y983', // iShares Core MSCI World UCITS ETF
    'LU0274208692', // Xtrackers MSCI World Information Technology UCITS ETF
    'FR0010688440', // Lyxor CAC 40 UCITS ETF
    
    // Fonds actions internationales
    'FR0010527275', // Amundi ETF MSCI Emerging Markets UCITS ETF
    'LU0908500753', // Xtrackers MSCI USA UCITS ETF
    'IE00B1XK9C88', // iShares Core MSCI Japan IMI UCITS ETF
    'IE00B4K48X80', // iShares Core MSCI Pacific ex Japan UCITS ETF
    
    // Fonds obligataires
    'FR0013412038', // Lyxor Green Bond UCITS ETF
    'LU0908501058', // Xtrackers Euro Government Bond UCITS ETF
    'IE00B1YZSC51', // iShares Core Euro Government Bond UCITS ETF
    'FR0010892224', // Amundi Euro Government Bond UCITS ETF
    
    // Fonds diversifiés et allocation
    'FR0010135103', // Amundi Patrimoine
    'FR0000284689', // CA Indosuez Patrimoine Equilibre
    'FR0000437956', // CA Indosuez Patrimoine Dynamique
    'FR0000437964', // CA Indosuez Patrimoine Prudent
    
    // Fonds sectoriels et thématiques
    'LU0533033238', // Xtrackers MSCI World Health Care UCITS ETF
    'IE00BM67HK77', // iShares Global Clean Energy UCITS ETF
    'LU0533032859', // Xtrackers MSCI World Consumer Discretionary UCITS ETF
    'FR0010342592', // Amundi ETF MSCI Europe Ex UK UCITS ETF
    
    // Fonds immobiliers (REIT)
    'FR0007085233', // Amundi REIT UCITS ETF
    'IE00B1FZS350', // iShares European Property Yield UCITS ETF
    
    // Fonds monétaires et court terme
    'FR0010510800', // Amundi Euro Liquidity SRI UCITS ETF
    'LU0290358497', // Xtrackers Euro Overnight Rate Swap UCITS ETF
    
    // Fonds responsables/ESG
    'LU0629459743', // Xtrackers MSCI World ESG UCITS ETF
    'IE00BFNM3J75', // iShares MSCI World ESG Enhanced UCITS ETF
    'FR0013412020', // Lyxor MSCI World ESG Leaders Extra UCITS ETF
];

try {
    $fondsManager = new FondsManager();
    
    echo "Synchronisation des fonds Floriane 2...\n";
    echo "Nombre de codes ISIN à traiter: " . count($floriane2Codes) . "\n\n";
    
    $results = $fondsManager->syncFonds($floriane2Codes);
    
    $synced = 0;
    $errors = [];
    
    foreach ($results as $result) {
        if (!isset($result['error'])) {
            $synced++;
            echo "✅ " . $result['nom'] . " (SRI: " . $result['sri'] . ", Note: " . number_format($result['note'], 1) . "/10)\n";
        } else {
            $errors[] = $result['error'];
            echo "⚠️  " . $result['error'] . "\n";
        }
    }
    
    echo "\n📈 Récapitulatif Floriane 2:\n";
    echo "- Fonds synchronisés: {$synced}\n";
    echo "- Erreurs: " . count($errors) . "\n";
    
    // Afficher tous les fonds en base
    $allFonds = $fondsManager->getAllFonds();
    echo "- Total fonds en base: " . count($allFonds) . "\n\n";
    
    // Statistiques par catégorie
    $categories = [];
    foreach ($allFonds as $fond) {
        if ($fond['code_isin'] !== 'EURO000000000') {
            $cat = $fond['categorie'];
            if (!isset($categories[$cat])) {
                $categories[$cat] = 0;
            }
            $categories[$cat]++;
        }
    }
    
    echo "🏷️  Répartition par catégorie:\n";
    foreach ($categories as $cat => $count) {
        echo "   • {$cat}: {$count} fonds\n";
    }
    
    echo "\n🎯 Top 10 des meilleurs fonds (par note):\n";
    $topFonds = array_filter($allFonds, function($f) { 
        return $f['code_isin'] !== 'EURO000000000'; 
    });
    usort($topFonds, function($a, $b) { 
        return $b['note'] <=> $a['note']; 
    });
    
    foreach (array_slice($topFonds, 0, 10) as $i => $fond) {
        $rank = $i + 1;
        echo "   {$rank}. " . $fond['nom'] . " - Note: " . $fond['note'] . "/10 (SRI: " . $fond['sri'] . ")\n";
    }
    
    echo "\n🌱 Fonds les plus responsables (SRI ≥ 7):\n";
    $fondsESG = array_filter($allFonds, function($f) { 
        return $f['sri'] >= 7 && $f['code_isin'] !== 'EURO000000000'; 
    });
    
    if (count($fondsESG) > 0) {
        foreach ($fondsESG as $fond) {
            echo "   • " . $fond['nom'] . " (SRI: " . $fond['sri'] . ", Note: " . $fond['note'] . "/10)\n";
        }
    } else {
        echo "   Aucun fonds avec SRI ≥ 7 trouvé.\n";
    }
    
    echo "\n🎉 Base de données mise à jour avec les fonds Floriane 2 !\n";
    echo "Vous pouvez maintenant utiliser l'application avec ces fonds réels.\n";
    echo "Accédez à http://localhost:8000 pour voir le résultat.\n";
    
} catch (Exception $e) {
    echo "❌ Erreur: " . $e->getMessage() . "\n";
}
?>
