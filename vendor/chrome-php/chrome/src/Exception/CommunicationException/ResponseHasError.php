<?php

/*
 * This file is part of Chrome PHP.
 *
 * (c) <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace HeadlessChromium\Exception\CommunicationException;

use HeadlessChromium\Exception\CommunicationException;

class ResponseHasError extends CommunicationException
{
}
