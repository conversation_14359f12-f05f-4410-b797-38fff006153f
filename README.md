# 📊 Analyseur de Fonds d'Investissement

Application PHP pour analyser et comparer des fonds d'investissement avec intégration API Quantalys.

## 🚀 Fonctionnalités

- **Synchronisation automatique** des données de fonds via API Quantalys (simulée)
- **Calcul de notes** sur 10 basé sur les ratios de Sharpe, Sortino et performance
- **Affichage interactif** avec tableau responsive et couleurs conditionnelles
- **Calcul en temps réel** du SRI moyen pondéré du portefeuille
- **Préconisation d'investissement** automatique avec contraintes SRI et diversification
- **Interface moderne** avec CSS et JavaScript

## 📋 Prérequis

- PHP 7.4 ou supérieur
- MySQL 5.7 ou supérieur
- Composer
- Extensions PHP : PDO, JSON, cURL

## 🛠️ Installation

1. **Cloner le projet** (ou copier les fichiers)
2. **Configurer la base de données** dans `config/config.php`
3. **Installer les dépendances** :
   ```bash
   composer install
   ```
4. **Configurer la base de données** :
   ```bash
   php setup.php
   ```
5. **Tester l'installation** :
   ```bash
   php test.php
   ```

## 🎯 Utilisation

### Interface Web

1. Ouvrir `index.php` dans votre navigateur
2. Saisir les codes ISIN dans le formulaire (séparés par des virgules)
3. Cliquer sur "Synchroniser les fonds"
4. Saisir les montants d'investissement dans la colonne de droite
5. Observer le récapitulatif et les préconisations en temps réel

### Codes ISIN de test

L'application inclut des données simulées pour ces codes :
- `FR0010315770` - Amundi MSCI World UCITS ETF
- `FR0013412038` - Lyxor Green Bond UCITS ETF  
- `LU0274208692` - Xtrackers MSCI World Information Technology UCITS ETF
- `FR0010688440` - Lyxor CAC 40 UCITS ETF
- `IE00B4L5Y983` - iShares Core MSCI World UCITS ETF
- `FR0010527275` - Amundi ETF MSCI Emerging Markets UCITS ETF

## 📊 Système de notation

### Calcul de la note (sur 10)
- **30%** Ratio de Sharpe
- **30%** Ratio de Sortino  
- **40%** Performance sur 3 ans

### Codes couleur
- **🟢 Vert** : Ratios > 0.7, Volatilité < 15%
- **🟠 Orange** : Ratios 0.5-0.7
- **🔴 Rouge** : Ratios < 0.5, Volatilité ≥ 15%

## 🎯 Préconisation d'investissement

L'algorithme génère automatiquement une allocation pour 1000€ :
- **20%** dans le fonds Euro (sécurité)
- **80%** répartis sur les 5 meilleurs fonds
- **SRI cible** : 4.0
- **Limite par fonds** : 35% maximum

## 🔧 Structure du projet

```
wallet/
├── config/
│   └── config.php          # Configuration BDD
├── src/
│   ├── Database.php         # Gestion base de données
│   ├── FondsManager.php     # Logique métier fonds
│   ├── QuantalysAPI.php     # Interface API (simulée)
│   └── Calculator.php       # Calculs et algorithmes
├── database/
│   └── schema.sql          # Structure de la base
├── assets/
│   ├── style.css           # Styles CSS
│   └── script.js           # JavaScript interactif
├── index.php               # Interface principale
├── sync.php                # Endpoint synchronisation
├── update-notes.php        # Endpoint mise à jour notes
├── setup.php               # Script d'installation
├── test.php                # Tests automatisés
└── composer.json           # Dépendances PHP
```

## 🔌 Intégration API Quantalys

### Configuration actuelle
L'application utilise actuellement des **données simulées** dans `src/QuantalysAPI.php`.

### Pour intégrer la vraie API Quantalys :
1. Obtenir une clé API auprès de Quantalys
2. Modifier la méthode `makeApiCall()` dans `QuantalysAPI.php`
3. Remplacer `getSimulatedData()` par de vrais appels API
4. Adapter le mapping des champs selon la documentation Quantalys

## 📱 Interface utilisateur

### Tableau principal
- **Tri automatique** par note décroissante
- **Fonds Euro** en première ligne (fond sécurisé)
- **Couleurs conditionnelles** pour tous les indicateurs
- **Champs de saisie** pour les montants d'investissement

### Récapitulatif en temps réel
- **Montant total** investi
- **SRI moyen** pondéré par les montants
- **Statistiques** du portefeuille

### Préconisation intelligente
- **Allocation optimisée** selon les contraintes
- **Visualisation claire** des recommandations
- **SRI total** du portefeuille recommandé

## 🧪 Tests

Lancer les tests automatisés :
```bash
php test.php
```

Les tests vérifient :
- Connexion base de données
- Synchronisation des fonds
- Calculs des notes
- Génération des préconisations

## 🔒 Sécurité

- **Requêtes préparées** pour éviter les injections SQL
- **Validation** des codes ISIN
- **Échappement HTML** dans l'affichage
- **Gestion d'erreurs** robuste

## 📈 Évolutions possibles

- Intégration API Quantalys réelle
- Historique des performances
- Graphiques interactifs
- Export PDF des analyses
- Alertes par email
- API REST pour applications mobiles

## 🆘 Support

En cas de problème :
1. Vérifier la configuration dans `config/config.php`
2. Lancer `php test.php` pour diagnostiquer
3. Consulter les logs d'erreur PHP
4. Vérifier les permissions de fichiers
