<?php
require_once 'vendor/autoload.php';

echo "🧪 Test de la librairie chrome-php\n";
echo "==================================\n\n";

try {
    echo "1. Test de l'autoloader...\n";
    if (class_exists('HeadlessChromium\\BrowserFactory')) {
        echo "✅ Classe BrowserFactory trouvée\n";
    } else {
        echo "❌ Classe BrowserFactory non trouvée\n";
        exit(1);
    }
    
    echo "\n2. Test de création d'instance...\n";
    $browserFactory = new HeadlessChromium\BrowserFactory();
    echo "✅ BrowserFactory instanciée\n";
    
    echo "\n3. Test de configuration...\n";
    $chromePath = '/Applications/Google Chrome.app/Contents/MacOS/Google Chrome';
    
    if (file_exists($chromePath)) {
        echo "✅ Chrome trouvé: {$chromePath}\n";
        
        $config = [
            'headless' => true,
            'noSandbox' => true,
            'keepAlive' => false,
            'connectionTimeout' => 10000,
            'arguments' => [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-gpu'
            ]
        ];
        
        echo "\n4. Test de création de navigateur...\n";
        try {
            $browserFactory = new HeadlessChromium\BrowserFactory($chromePath);
            $browser = $browserFactory->createBrowser($config);
            echo "✅ Navigateur créé avec succès\n";
            
            echo "\n5. Test de création de page...\n";
            $page = $browser->createPage();
            echo "✅ Page créée avec succès\n";
            
            echo "\n6. Test de navigation simple...\n";
            $navigation = $page->navigate('data:text/html,<html><body><h1>Test</h1></body></html>');
            $navigation->waitForNavigation();
            echo "✅ Navigation réussie\n";
            
            echo "\n7. Test d'extraction de contenu...\n";
            $title = $page->dom()->querySelector('h1')->getText();
            echo "✅ Contenu extrait: '{$title}'\n";
            
            $browser->close();
            echo "\n🎉 Tous les tests sont passés ! Le robot est prêt.\n";
            
        } catch (Exception $e) {
            echo "❌ Erreur lors du test: " . $e->getMessage() . "\n";
        }
        
    } else {
        echo "❌ Chrome non trouvé à l'emplacement: {$chromePath}\n";
    }
    
} catch (Exception $e) {
    echo "❌ Erreur: " . $e->getMessage() . "\n";
}
?>
