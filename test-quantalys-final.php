<?php
require_once 'config/config.php';
require_once 'vendor/autoload.php';

use Wallet\QuantalysRobotServiceFinal;
use Wallet\Database;
use Wallet\Calculator;

echo "🤖 Test final du robot Quantalys\n";
echo "================================\n\n";

// Codes ISIN de test (échantillon du contrat Floriane 2)
$testCodes = [
    'FR0013048287', // Acticcia Vie 90 N°3 C (testé localement)
    'FR001400STS3',
    'FR0012287316', 
    'FR0012829364',
    'FR0013118692'
];

try {
    $robot = new QuantalysRobotServiceFinal();
    $db = Database::getInstance();
    
    echo "🔍 Test avec " . count($testCodes) . " codes ISIN...\n\n";
    
    $successCount = 0;
    $errorCount = 0;
    
    foreach ($testCodes as $index => $isin) {
        echo "📊 Test " . ($index + 1) . "/" . count($testCodes) . " - ISIN: {$isin}\n";
        echo str_repeat("-", 50) . "\n";
        
        // Récupérer les données via le robot
        $fondData = $robot->getFondDataByISIN($isin);
        
        if (isset($fondData['error'])) {
            echo "❌ Erreur: " . $fondData['error'] . "\n\n";
            $errorCount++;
            continue;
        }
        
        // Afficher les données récupérées
        echo "✅ Données récupérées:\n";
        echo "   • Nom: " . $fondData['nom'] . "\n";
        echo "   • Catégorie: " . $fondData['categorie'] . "\n";
        echo "   • Performance 3 ans: " . $fondData['performance_3ans'] . "%\n";
        echo "   • Volatilité: " . $fondData['volatilite'] . "%\n";
        echo "   • Ratio Sharpe: " . $fondData['ratio_sharpe'] . "\n";
        echo "   • Ratio Sortino: " . $fondData['ratio_sortino'] . "\n";
        echo "   • SRI: " . $fondData['sri'] . "\n";
        
        // Calculer la note
        $note = Calculator::calculateNote($fondData);
        echo "   • Note calculée: " . $note . "/10\n";
        
        // Mettre à jour en base de données
        try {
            $existing = $db->fetchOne("SELECT id FROM fonds WHERE code_isin = ?", [$isin]);
            
            $dataToSave = [
                'code_isin' => $isin,
                'nom' => $fondData['nom'],
                'categorie' => $fondData['categorie'],
                'performance_3ans' => $fondData['performance_3ans'],
                'volatilite' => $fondData['volatilite'],
                'ratio_sharpe' => $fondData['ratio_sharpe'],
                'ratio_sortino' => $fondData['ratio_sortino'],
                'sri' => $fondData['sri'],
                'note' => $note
            ];
            
            if ($existing) {
                $db->update('fonds', $dataToSave, 'code_isin = ?', [$isin]);
                echo "   📝 Fonds mis à jour en base de données\n";
            } else {
                $db->insert('fonds', $dataToSave);
                echo "   📝 Fonds ajouté en base de données\n";
            }
            
            $successCount++;
            
        } catch (Exception $e) {
            echo "   ❌ Erreur BDD: " . $e->getMessage() . "\n";
            $errorCount++;
        }
        
        echo "\n";
        
        // Pause entre les requêtes pour éviter la surcharge
        if ($index < count($testCodes) - 1) {
            echo "⏳ Pause de 5 secondes...\n\n";
            sleep(5);
        }
    }
    
    echo "🎉 Test terminé !\n";
    echo "================\n";
    echo "✅ Succès: {$successCount}\n";
    echo "❌ Erreurs: {$errorCount}\n";
    echo "📈 Taux de succès: " . round(($successCount / count($testCodes)) * 100, 1) . "%\n\n";
    
    echo "📋 Prochaines étapes:\n";
    echo "1. Consultez les logs dans logs/quantalys-robot.log\n";
    echo "2. Vérifiez l'application sur http://localhost:8000\n";
    echo "3. Lancez le scraping complet avec scrape-floriane2-real.php\n";
    
} catch (Exception $e) {
    echo "❌ Erreur générale: " . $e->getMessage() . "\n";
}
?>
