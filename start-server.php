<?php
echo "🚀 Démarrage du serveur de développement\n";
echo "======================================\n\n";

// Vérifier que PHP est disponible
if (!function_exists('exec')) {
    echo "❌ La fonction exec() n'est pas disponible\n";
    exit(1);
}

// Port par défaut
$port = 8000;
$host = 'localhost';

// Vérifier si le port est libre
$connection = @fsockopen($host, $port);
if (is_resource($connection)) {
    fclose($connection);
    echo "⚠️  Le port {$port} est déjà utilisé, essai du port suivant...\n";
    $port++;
}

echo "📡 Démarrage du serveur sur http://{$host}:{$port}\n";
echo "📁 Répertoire: " . __DIR__ . "\n";
echo "🛑 Appuyez sur Ctrl+C pour arrêter le serveur\n\n";

// Démarrer le serveur PHP intégré
$command = "php -S {$host}:{$port} -t " . __DIR__;
echo "Commande: {$command}\n\n";

// Ouvrir le navigateur (optionnel)
if (PHP_OS_FAMILY === 'Darwin') { // macOS
    exec("open http://{$host}:{$port}");
} elseif (PHP_OS_FAMILY === 'Windows') {
    exec("start http://{$host}:{$port}");
} elseif (PHP_OS_FAMILY === 'Linux') {
    exec("xdg-open http://{$host}:{$port}");
}

// Exécuter la commande
passthru($command);
?>
