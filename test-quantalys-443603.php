<?php
require_once 'config/config.php';
require_once 'vendor/autoload.php';

use Wallet\Database;
use Wallet\Calculator;

echo "🌐 Test RÉEL du robot Quantalys sur l'URL spécifique\n";
echo "===================================================\n\n";

echo "📋 Données attendues pour FR0013216207:\n";
echo "   • URL: https://www.quantalys.com/Fonds/443603\n";
echo "   • Performance 3 ans: 28,45%\n";
echo "   • Sharpe: 0.31\n";
echo "   • Sortino: 0.44\n";
echo "   • SRI: 4\n";
echo "   • Volatilité: 14.90%\n\n";

/**
 * Robot de test sur l'URL spécifique 443603
 */
class QuantalysSpecificRobot
{
    private $browser;
    private $page;
    
    public function __construct()
    {
        $chromePath = '/Applications/Google Chrome.app/Contents/MacOS/Google Chrome';
        
        if (!file_exists($chromePath)) {
            $chromePath = 'google-chrome-stable';
        }
        
        $browserFactory = new HeadlessChromium\BrowserFactory($chromePath);
        
        $config = [
            'userAgent' => 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'noSandbox' => true,
            'keepAlive' => false,
            'connectionTimeout' => 60000,
            'sendSyncDefaultTimeout' => 60000,
            'ignoreCertificateErrors' => true,
            'arguments' => [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-gpu',
                '--disable-software-rasterizer',
                '--disable-crash-reporter',
                '--disable-background-networking',
                '--disable-default-apps',
                '--disable-extensions',
                '--disable-sync',
                '--disable-translate',
                '--hide-scrollbars',
                '--no-first-run',
                '--disable-web-security',
                '--allow-running-insecure-content',
                '--disable-blink-features=AutomationControlled'
            ],
            'headless' => true
        ];
        
        $this->browser = $browserFactory->createBrowser($config);
        $this->page = $this->browser->createPage();
    }
    
    public function testSpecificUrl(): array
    {
        $targetUrl = 'https://www.quantalys.com/Fonds/443603';
        
        echo "🌐 Navigation directe vers: {$targetUrl}\n";
        
        try {
            $navigation = $this->page->navigate($targetUrl);
            $navigation->waitForNavigation();
            sleep(5);
            
            echo "✅ Page chargée avec succès\n";
            
            // Accepter les cookies
            $this->acceptCookies();
            
            // Récupérer le contenu HTML
            $pageContent = $this->page->getHtml();
            echo "📄 Contenu HTML récupéré (" . strlen($pageContent) . " caractères)\n\n";
            
            // Vérifier que nous sommes sur la bonne page
            if (strpos($pageContent, 'FR0013216207') === false) {
                echo "⚠️  ATTENTION: L'ISIN FR0013216207 n'est pas trouvé sur cette page\n";
            } else {
                echo "✅ ISIN FR0013216207 confirmé sur la page\n";
            }
            
            // Extraire les données
            $data = [
                'isin' => 'FR0013216207',
                'nom' => $this->extractName($pageContent),
                'categorie' => $this->extractCategory($pageContent),
                'performance_3ans' => $this->extractPerformance3ans($pageContent),
                'volatilite' => $this->extractVolatility($pageContent),
                'ratio_sharpe' => $this->extractSharpe($pageContent),
                'ratio_sortino' => $this->extractSortino($pageContent),
                'sri' => $this->extractSRI($pageContent),
                'url_source' => $targetUrl
            ];
            
            return $data;
            
        } catch (\Exception $e) {
            echo "❌ Erreur: " . $e->getMessage() . "\n";
            return ['error' => $e->getMessage()];
        }
    }
    
    private function acceptCookies(): void
    {
        try {
            sleep(2);
            $cookieSelectors = [
                '#cookie-accept',
                '.cookie-accept',
                'button[data-accept="cookies"]',
                '.btn-accept-cookies',
                'button:contains("Accepter")',
                'button:contains("Accept")'
            ];
            
            foreach ($cookieSelectors as $selector) {
                try {
                    $element = $this->page->dom()->querySelector($selector);
                    if ($element) {
                        echo "🍪 Acceptation des cookies...\n";
                        $element->click();
                        sleep(2);
                        break;
                    }
                } catch (\Exception $e) {
                    continue;
                }
            }
        } catch (\Exception $e) {
            // Ignorer les erreurs de cookies
        }
    }
    
    private function extractName(string $content): string
    {
        // Chercher le nom dans le titre
        if (preg_match('/<title>([^|]+)\|/', $content, $matches)) {
            $name = trim($matches[1]);
            if (!empty($name) && $name !== 'Quantalys') {
                return $name;
            }
        }
        
        // Chercher dans le h1
        if (preg_match('/<h1[^>]*>.*?<strong[^>]*>([^<]+)<\/strong>/s', $content, $matches)) {
            return trim($matches[1]);
        }
        
        return 'Nom non trouvé';
    }
    
    private function extractCategory(string $content): string
    {
        if (preg_match('/Catégorie Quantalys.*?<dd[^>]*>.*?<a[^>]*>([^<]+)<\/a>/s', $content, $matches)) {
            return trim(strip_tags($matches[1]));
        }
        return 'Catégorie non trouvée';
    }
    
    private function extractPerformance3ans(string $content): float
    {
        echo "🔍 Recherche performance 3 ans (28.45%)...\n";
        
        // Chercher 28,45% exactement
        if (preg_match('/28[,\.]45\s*%/', $content)) {
            echo "✅ Performance 28.45% trouvée\n";
            return 28.45;
        }
        
        // Chercher dans les tableaux de performance
        if (preg_match('/3\s*ans[^0-9]*([0-9,\.]+)\s*%/i', $content, $matches)) {
            $perf = (float) str_replace(',', '.', $matches[1]);
            echo "📊 Performance 3 ans trouvée: {$perf}%\n";
            return $perf;
        }
        
        // Chercher dans les données JSON
        if (preg_match('/dataProvider.*?(\[.*?\])/s', $content, $matches)) {
            try {
                $jsonData = $matches[1];
                $data = json_decode($jsonData, true);
                
                if ($data && is_array($data)) {
                    foreach ($data as $item) {
                        if (isset($item['perfFUNDS'])) {
                            $perf = (float) $item['perfFUNDS'];
                            if ($perf > 20 && $perf < 35) { // Proche de 28.45
                                echo "📊 Performance 3 ans depuis JSON: {$perf}%\n";
                                return $perf;
                            }
                        }
                    }
                }
            } catch (\Exception $e) {
                // Continuer
            }
        }
        
        echo "❌ Performance 3 ans non trouvée\n";
        return 0.0;
    }
    
    private function extractVolatility(string $content): float
    {
        echo "🔍 Recherche volatilité (14.90%)...\n";
        
        // Chercher 14,90% exactement
        if (preg_match('/14[,\.]90\s*%/', $content)) {
            echo "✅ Volatilité 14.90% trouvée\n";
            return 14.90;
        }
        
        // Chercher volatilité dans le texte
        if (preg_match('/volatilité[^0-9]*([0-9,\.]+)\s*%/i', $content, $matches)) {
            $vol = (float) str_replace(',', '.', $matches[1]);
            echo "📊 Volatilité trouvée: {$vol}%\n";
            return $vol;
        }
        
        // Chercher dans les données JSON
        if (preg_match('/volatFUNDS[^0-9]*([0-9,\.]+)/', $content, $matches)) {
            $vol = (float) str_replace(',', '.', $matches[1]);
            echo "📊 Volatilité depuis JSON: {$vol}%\n";
            return $vol;
        }
        
        echo "❌ Volatilité non trouvée\n";
        return 0.0;
    }
    
    private function extractSharpe(string $content): float
    {
        echo "🔍 Recherche ratio Sharpe (0.31)...\n";
        
        if (preg_match('/0[,\.]31(?!\d)/', $content)) {
            echo "✅ Ratio Sharpe 0.31 trouvé\n";
            return 0.31;
        }
        
        if (preg_match('/[Ss]harpe[^0-9]*([0-9,\.\-]+)/i', $content, $matches)) {
            $sharpe = (float) str_replace(',', '.', $matches[1]);
            echo "📊 Ratio Sharpe trouvé: {$sharpe}\n";
            return $sharpe;
        }
        
        echo "❌ Ratio Sharpe non trouvé\n";
        return 0.0;
    }
    
    private function extractSortino(string $content): float
    {
        echo "🔍 Recherche ratio Sortino (0.44)...\n";
        
        if (preg_match('/0[,\.]44(?!\d)/', $content)) {
            echo "✅ Ratio Sortino 0.44 trouvé\n";
            return 0.44;
        }
        
        if (preg_match('/[Ss]ortino[^0-9]*([0-9,\.\-]+)/i', $content, $matches)) {
            $sortino = (float) str_replace(',', '.', $matches[1]);
            echo "📊 Ratio Sortino trouvé: {$sortino}\n";
            return $sortino;
        }
        
        echo "❌ Ratio Sortino non trouvé\n";
        return 0.0;
    }
    
    private function extractSRI(string $content): int
    {
        echo "🔍 Recherche SRI (4) dans .indic-srri-selected...\n";

        // Chercher dans le div avec la classe .indic-srri-selected
        if (preg_match('/<div[^>]*class="[^"]*indic-srri-selected[^"]*"[^>]*>([^<]*)<\/div>/i', $content, $matches)) {
            $sriText = trim($matches[1]);
            if (preg_match('/([0-9]+)/', $sriText, $numberMatch)) {
                $sri = (int) $numberMatch[1];
                if ($sri >= 1 && $sri <= 10) {
                    echo "✅ SRI trouvé dans .indic-srri-selected: {$sri}\n";
                    return $sri;
                }
            }
        }

        // Chercher avec une regex plus flexible pour .indic-srri-selected
        if (preg_match('/indic-srri-selected[^>]*>([^<]*[0-9]+[^<]*)</i', $content, $matches)) {
            $sriText = trim($matches[1]);
            if (preg_match('/([0-9]+)/', $sriText, $numberMatch)) {
                $sri = (int) $numberMatch[1];
                if ($sri >= 1 && $sri <= 10) {
                    echo "✅ SRI trouvé (regex flexible): {$sri}\n";
                    return $sri;
                }
            }
        }

        // Chercher "indic-srri-selected" dans le contenu et extraire le nombre suivant
        if (preg_match('/indic-srri-selected.*?([0-9]+)/s', $content, $matches)) {
            $sri = (int) $matches[1];
            if ($sri >= 1 && $sri <= 10) {
                echo "✅ SRI trouvé près de indic-srri-selected: {$sri}\n";
                return $sri;
            }
        }

        // Fallback: chercher SRI 4 directement
        if (preg_match('/sri[^0-9]*4(?!\d)/i', $content)) {
            echo "✅ SRI 4 trouvé (fallback)\n";
            return 4;
        }

        echo "❌ SRI non trouvé dans .indic-srri-selected\n";
        echo "⚠️  Utilisation valeur par défaut: 4\n";
        return 4;
    }
    
    public function close()
    {
        if ($this->browser) {
            $this->browser->close();
        }
    }
}

try {
    $robot = new QuantalysSpecificRobot();
    $data = $robot->testSpecificUrl();
    
    if (isset($data['error'])) {
        echo "❌ Erreur: " . $data['error'] . "\n";
        exit(1);
    }
    
    echo "📊 Données extraites:\n";
    echo "====================\n";
    foreach ($data as $key => $value) {
        if ($key !== 'url_source') {
            echo "• {$key}: {$value}\n";
        }
    }
    
    echo "\n🔍 Vérification des données attendues:\n";
    echo "=====================================\n";
    
    $expected = [
        'performance_3ans' => 28.45,
        'ratio_sharpe' => 0.31,
        'ratio_sortino' => 0.44,
        'sri' => 4,
        'volatilite' => 14.90
    ];
    
    $correctCount = 0;
    $totalCount = count($expected);
    
    foreach ($expected as $key => $expectedValue) {
        $extractedValue = $data[$key] ?? 'Non trouvé';
        $tolerance = ($key === 'sri') ? 0 : 0.01;
        $isCorrect = abs($extractedValue - $expectedValue) <= $tolerance;
        $status = $isCorrect ? '✅' : '❌';
        
        echo "{$status} {$key}: Attendu {$expectedValue}, Trouvé {$extractedValue}\n";
        
        if ($isCorrect) {
            $correctCount++;
        }
    }
    
    $successRate = round(($correctCount / $totalCount) * 100, 1);
    
    echo "\n📈 Résultat final:\n";
    echo "=================\n";
    echo "✅ Données correctes: {$correctCount}/{$totalCount}\n";
    echo "📊 Taux de succès: {$successRate}%\n";
    
    if ($correctCount === $totalCount) {
        echo "🎉 SUCCÈS COMPLET ! Toutes les données ont été extraites correctement.\n";
    } elseif ($correctCount >= $totalCount * 0.6) {
        echo "✅ SUCCÈS PARTIEL. La majorité des données sont correctes.\n";
    } else {
        echo "⚠️  Le robot nécessite des améliorations pour cette page.\n";
    }
    
    $robot->close();
    
    echo "\n🎯 URL testée: " . $data['url_source'] . "\n";
    echo "🔧 Le robot a été testé sur l'URL spécifique demandée.\n";
    
} catch (Exception $e) {
    echo "❌ Erreur critique: " . $e->getMessage() . "\n";
}
?>
