<?php
require_once 'config/config.php';
require_once 'vendor/autoload.php';

use Wallet\Database;
use Wallet\Calculator;

echo "🔍 Test FINAL du robot Quantalys avec ISIN fonctionnel\n";
echo "====================================================\n\n";

// Essayons avec des ISINs plus communs
$testISINs = [
    'FR0010315770', // Amundi CAC 40 UCITS ETF
    //'FR0000988040', // BNP Paribas Aqua
    //'FR0012287316', // Un des ISINs de la liste Floriane 2
    //'FR0013216207'  // L'ISIN original
];

echo "📋 Test avec plusieurs ISINs pour trouver un qui fonctionne:\n";
foreach ($testISINs as $i => $isin) {
    echo "   " . ($i + 1) . ". {$isin}\n";
}
echo "\n";

/**
 * Robot final avec processus complet
 */
class QuantalysFinalRobot
{
    private $browser;
    private $page;

    public function __construct()
    {
        $chromePath = '/Applications/Google Chrome.app/Contents/MacOS/Google Chrome';

        if (!file_exists($chromePath)) {
            $chromePath = 'google-chrome-stable';
        }

        $browserFactory = new HeadlessChromium\BrowserFactory($chromePath);

        $config = [
            'userAgent' => 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'noSandbox' => true,
            'keepAlive' => false,
            'connectionTimeout' => 60000,
            'sendSyncDefaultTimeout' => 60000,
            'ignoreCertificateErrors' => true,
            /*'arguments' => [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-gpu',
                '--disable-software-rasterizer',
                '--disable-crash-reporter',
                '--disable-background-networking',
                '--disable-default-apps',
                '--disable-extensions',
                '--disable-sync',
                '--disable-translate',
                '--hide-scrollbars',
                '--no-first-run',
                '--disable-web-security',
                '--allow-running-insecure-content',
                '--disable-blink-features=AutomationControlled'
            ],*/
            'headless' => false
        ];

        $this->browser = $browserFactory->createBrowser($config);
        $this->page = $this->browser->createPage();
    }

    public function testMultipleISINs(array $isins): array
    {
        foreach ($isins as $i => $isin) {
            echo "🔍 Test " . ($i + 1) . "/" . count($isins) . " - ISIN: {$isin}\n";
            echo str_repeat("-", 50) . "\n";

            $result = $this->searchAndExtractData($isin);

            if (!isset($result['error'])) {
                echo "🎉 SUCCÈS avec l'ISIN: {$isin}\n";
                return $result;
            } else {
                echo "❌ Échec avec {$isin}: " . $result['error'] . "\n\n";
            }
        }

        return ['error' => 'Aucun ISIN n\'a fonctionné'];
    }

    public function searchAndExtractData(string $isin): array
    {
        try {
            // Aller sur la page de recherche
            $searchUrl = "https://www.quantalys.com/Recherche";

            echo "🌐 Navigation vers la page de recherche...\n";
            $navigation = $this->page->navigate($searchUrl);
            $navigation->waitForNavigation();
            sleep(5);

            // Accepter les cookies
            echo "🍪 Acceptation des cookies...\n";
            $this->acceptCookies();

            // Cocher toutes les cases
            echo "☑️  Cocher toutes les cases requises...\n";
            $this->checkAllRequiredBoxes();

            // Saisir l'ISIN
            if (!$this->fillSearchField($isin)) {
                return ['error' => "Impossible de saisir l'ISIN"];
            }

            // Cliquer sur rechercher
            if (!$this->clickSearchButton()) {
                return ['error' => "Impossible de cliquer sur rechercher"];
            }

            // Trouver et cliquer sur le premier résultat
            $fondUrl = $this->findAndClickFirstResult($isin);
            if (!$fondUrl) {
                return ['error' => "Aucun résultat trouvé"];
            }

            echo "✅ Fonds trouvé: {$fondUrl}\n";

            $navigation = $this->page->navigate($fondUrl);
            $navigation->waitForNavigation();

            // Attendre le chargement
            sleep(2);

            // Extraire les données
            $pageContent = $this->page->getHtml();
            //var_dump($pageContent);

            $data = [
                'isin' => $isin,
                'nom' => $this->extractName($pageContent),
                'categorie' => $this->extractCategory($pageContent),
                'performance_3ans' => $this->extractPerformance3ans($pageContent),
                'volatilite' => $this->extractVolatility($pageContent),
                'ratio_sharpe' => $this->extractSharpe($pageContent),
                'ratio_sortino' => $this->extractSortino($pageContent),
                'sri' => $this->extractSRI($pageContent),
                'url_source' => $fondUrl
            ];

            return $data;

        } catch (\Exception $e) {
            return ['error' => $e->getMessage()];
        }
    }

    private function acceptCookies(): void
    {
        try {
            sleep(2);
            $cookieSelectors = [
                'button.tarteaucitronCTAButton',
                '#cookie-accept',
                '.cookie-accept',
                'button[data-accept="cookies"]',
                '.btn-accept-cookies'
            ];

            foreach ($cookieSelectors as $selector) {
                try {
                    $element = $this->page->dom()->querySelector($selector);
                    if ($element) {
                        echo "clic sur $selector\n";
                        $element->click();
                        sleep(2);
                        break;
                    }
                } catch (\Exception $e) {
                    continue;
                }
            }
        } catch (\Exception $e) {
            // Ignorer
        }
    }

    private function checkAllRequiredBoxes(): bool
    {
        $checkboxes = [
            'input[name="chkTypeProduits"][value="1"]',
            'input[name="chkTypeProduits"][value="2"]',
            'input[name="chkTypeProduits"][value="4"]',
            'input[name="Values.bETF"]',
            'input[name="Values.bFCPE"]',
            'input[name="Values.bFCPR"]'
        ];

        foreach ($checkboxes as $selector) {
            echo "analyse de $selector\n";
            try {
                $checkbox = $this->page->dom()->querySelector($selector);
                if ($checkbox && ($checkbox->getAttribute('checked') === null || $checkbox->getAttribute('checked') === '')) {
                    $checkbox->click();
                    echo "clic $selector\n";
                    sleep(1);
                }
            } catch (\Exception $e) {
                // Continuer
            }
        }

        return true;
    }

    private function fillSearchField(string $isin): bool
    {
        try {
            $searchInput = $this->page->dom()->querySelector('input.js-typeahead.qt-search-input-typeahead');
            if (!$searchInput) {
                echo "non trouvé\n";
                sleep(5);
            }
            if (!$searchInput) {
                echo "non trouvé2\n";
                sleep(500);
            }
            $searchInput = $this->page->dom()->querySelector('input.js-typeahead.qt-search-input-typeahead');
            if ($searchInput) {
                $searchInput->sendKeys('');
                sleep(1);
                echo "saisie $isin\n";
                $searchInput->sendKeys($isin);
                return true;
            }
        } catch (\Exception $e) {
            echo $e->getMessage();
            // Erreur
        }

        return false;
    }

    private function clickSearchButton(): bool
    {
        try {
            $searchButton = $this->page->dom()->querySelector('button.qt-search-btn-search');
            if ($searchButton) {
                echo "clic search\n";
                $searchButton->click();
                return true;
            }
        } catch (\Exception $e) {
            // Erreur
        }
        return false;
    }

    private function findAndClickFirstResult(string $isin): ?string
    {
        try {
            sleep(5);

            $selectors = [
                '#quantasearch .qt-search-table tbody tr:first td.quantasearch-col-nom a',
                '.qt-search-table tbody tr:first-child .quantasearch-col-nom a',
                '.quantasearch-col-nom a'
            ];

            foreach ($selectors as $selector) {
                echo "test avec $selector\n";
                try {
                    $firstLink = $this->page->dom()->querySelector($selector);
                    if ($firstLink) {
                        echo "lien trouvé\n";
                        $href = $firstLink->getAttribute('href');
                        if (strpos($href, 'http') !== 0) {
                            $href = 'https://www.quantalys.com' . $href;
                        }

                        return $href;
                    }
                } catch (\Exception $e) {
                    continue;
                }
            }

            return null;

        } catch (\Exception $e) {
            return null;
        }
    }

    private function extractName(string $content): string
    {
        if (preg_match('/<h1[^>]*>.*?<strong[^>]*>([^<]+)<\/strong>/s', $content, $matches)) {
            return trim($matches[1]);
        }
        if (preg_match('/<h1[^>]*>([^<]+)<\/h1>/s', $content, $matches)) {
            $name = trim($matches[1]);
            // Nettoyer le nom
            $name = preg_replace('/^OPCVM\s*-\s*/', '', $name);
            $name = preg_replace('/\s*-\s*[A-Z]{2}[0-9A-Z]{10}.*$/', '', $name);
            return $name;
        }
        return 'Nom non trouvé';
    }

    private function extractCategory(string $content): string
    {
        if (preg_match('/Catégorie Quantalys.*?<dd[^>]*>.*?<a[^>]*>([^<]+)<\/a>/s', $content, $matches)) {
            return trim(strip_tags($matches[1]));
        }
        if (preg_match('/Classification AMF.*?<dd[^>]*>.*?<a[^>]*>([^<]+)<\/a>/s', $content, $matches)) {
            return trim(strip_tags($matches[1]));
        }
        return 'Catégorie non trouvée';
    }

    private function extractPerformance3ans(string $content): float
    {
        // Chercher dans les données du graphique JSON
        if (preg_match('/dataProvider.*?(\[.*?\])/s', $content, $matches)) {
            try {
                $jsonData = $matches[1];
                $data = json_decode($jsonData, true);

                if ($data && is_array($data) && count($data) > 1) {
                    $firstValue = $data[0]['y_0'] ?? 100;
                    $lastValue = end($data)['y_0'] ?? 100;

                    if ($firstValue > 0) {
                        return round((($lastValue / $firstValue) - 1) * 100, 2);
                    }
                }
            } catch (\Exception $e) {
                return 0.0;
            }
        }

        // Chercher dans le texte
        if (preg_match('/performance[^0-9]*([0-9,\.\-]+)%/i', $content, $matches)) {
            return $this->parsePercentage($matches[1]);
        }
        return 0.0;
    }

    private function extractVolatility(string $content): float
    {
        if (preg_match('/volatilité[^0-9]*([0-9,\.]+)\s*%/i', $content, $matches)) {
            return (float) str_replace(',', '.', $matches[1]);
        }
        return 0.0;
    }

    private function extractSharpe(string $content): float
    {
        if (preg_match('/[Ss]harpe[^0-9]*([0-9,\.\-]+)/i', $content, $matches)) {
            return (float) str_replace(',', '.', $matches[1]);
        }
        return 0.0;
    }

    private function extractSortino(string $content): float
    {
        if (preg_match('/[Ss]ortino[^0-9]*([0-9,\.\-]+)/i', $content, $matches)) {
            return (float) str_replace(',', '.', $matches[1]);
        }
        return 0.0;
    }

    private function extractSRI(string $content): int
    {
        // Chercher dans le div avec la classe .indic-srri-selected
        if (preg_match('/<div[^>]*class="[^"]*indic-srri-selected[^"]*"[^>]*>([^<]*)<\/div>/i', $content, $matches)) {
            $sriText = trim($matches[1]);
            if (preg_match('/([0-9]+)/', $sriText, $numberMatch)) {
                $sri = (int) $numberMatch[1];
                if ($sri >= 1 && $sri <= 10) {
                    $this->log("SRI trouvé dans .indic-srri-selected: {$sri}");
                    return $sri;
                }
            }
        }

        // Chercher avec une regex plus flexible pour .indic-srri-selected
        if (preg_match('/indic-srri-selected[^>]*>([^<]*[0-9]+[^<]*)</i', $content, $matches)) {
            $sriText = trim($matches[1]);
            if (preg_match('/([0-9]+)/', $sriText, $numberMatch)) {
                $sri = (int) $numberMatch[1];
                if ($sri >= 1 && $sri <= 10) {
                    $this->log("SRI trouvé (regex flexible): {$sri}");
                    return $sri;
                }
            }
        }

        // Chercher "indic-srri-selected" dans le contenu et extraire le nombre suivant
        if (preg_match('/indic-srri-selected.*?([0-9]+)/s', $content, $matches)) {
            $sri = (int) $matches[1];
            if ($sri >= 1 && $sri <= 10) {
                $this->log("SRI trouvé près de indic-srri-selected: {$sri}");
                return $sri;
            }
        }

        // Fallback: Basé sur l'évaluation ISR
        if (strpos($content, 'Non ISR') !== false) {
            $this->log("SRI basé sur 'Non ISR': 3");
            return 3; // Score faible pour "Non ISR"
        }

        // Chercher un score SRI explicite
        if (preg_match('/sri[^0-9]*([0-9]+)/i', $content, $matches)) {
            $sri = (int) $matches[1];
            if ($sri >= 1 && $sri <= 10) {
                $this->log("SRI trouvé (fallback): {$sri}");
                return $sri;
            }
        }

        // Basé sur SFDR
        if (strpos($content, 'Article 8') !== false || strpos($content, 'Article 9') !== false) {
            $this->log("SRI basé sur SFDR: 7");
            return 7; // Score élevé pour fonds durables
        }


        return 0;
    }

    public function close()
    {
        if ($this->browser) {
            $this->browser->close();
        }
    }

    /**
     * Écrit dans le fichier de log
     */
    private function log(string $message): void
    {
        //$timestamp = date('Y-m-d H:i:s');
        //$logMessage = "[{$timestamp}] {$message}\n";
        //file_put_contents($this->logFile, $logMessage, FILE_APPEND | LOCK_EX);
        echo $message . "\n";
    }

    /**
     * Parse un pourcentage depuis un texte
     */
    private function parsePercentage(string $text): float
    {
        $text = str_replace(['%', ' ', ','], ['', '', '.'], $text);
        $text = preg_replace('/[^\d\.\-\+]/', '', $text);

        if (is_numeric($text)) {
            return (float) $text;
        }

        return 0.0;
    }

    /**
     * Parse un nombre décimal depuis un texte
     */
    private function parseDecimal(string $text): float
    {
        $text = str_replace([' ', ','], ['', '.'], $text);
        $text = preg_replace('/[^\d\.\-\+]/', '', $text);

        if (is_numeric($text)) {
            return (float) $text;
        }

        return 0.0;
    }
}

try {
    $robot = new QuantalysFinalRobot();
    $data = $robot->testMultipleISINs($testISINs);

    var_dump($data);
    /*if (isset($data['error'])) {
        echo "❌ Erreur finale: " . $data['error'] . "\n";
        exit(1);
    }

    echo "\n🎉 SUCCÈS ! Données extraites:\n";
    echo "=============================\n";
    foreach ($data as $key => $value) {
        if ($key !== 'url_source') {
            echo "• {$key}: {$value}\n";
        }
    }

    echo "\n🎯 URL source: " . $data['url_source'] . "\n";

   // $robot->close();

    echo "\n✅ Le robot fonctionne parfaitement !\n";
    echo "🤖 Processus complet validé :\n";
    echo "   1. ✅ Navigation vers la page de recherche\n";
    echo "   2. ✅ Cochage de toutes les cases requises\n";
    echo "   3. ✅ Saisie de l'ISIN\n";
    echo "   4. ✅ Clic sur rechercher\n";
    echo "   5. ✅ Sélection du premier résultat\n";
    echo "   6. ✅ Extraction des données\n";*/

} catch (Exception $e) {
    echo "❌ Erreur critique: " . $e->getMessage() . "\n";
}
?>
