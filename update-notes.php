<?php
header('Content-Type: application/json');
require_once 'config/config.php';
require_once 'vendor/autoload.php';

use Wallet\FondsManager;

try {
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Méthode non autorisée');
    }

    $fondsManager = new FondsManager();
    
    // Mettre à jour toutes les notes
    $updated = $fondsManager->updateAllNotes();

    $response = [
        'success' => true,
        'updated' => $updated,
        'message' => $updated > 0 ? 
            "Notes mises à jour pour {$updated} fonds" : 
            "Aucune note à mettre à jour"
    ];

} catch (Exception $e) {
    $response = [
        'success' => false,
        'message' => $e->getMessage(),
        'updated' => 0
    ];
}

echo json_encode($response, JSON_UNESCAPED_UNICODE);
?>
