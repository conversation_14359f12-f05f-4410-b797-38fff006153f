<?php
require_once 'config/config.php';
require_once 'vendor/autoload.php';

use Wallet\Database;
use Wallet\FondsManager;
use Wallet\Calculator;

// Initialisation
try {
    $fondsManager = new FondsManager();
    
    // Initialiser la base de données si nécessaire
    $fondsManager->initializeDatabase();
    
    // Récupérer tous les fonds
    $fonds = $fondsManager->getAllFonds();
    
    // Générer une préconisation pour 1000€
    $recommendation = Calculator::generateRecommendation($fonds, 1000);
    
    // Statistiques
    $stats = $fondsManager->getStatistics();
    
} catch (Exception $e) {
    $error = "Erreur : " . $e->getMessage();
    $fonds = [];
    $recommendation = [];
    $stats = [];
}

// Codes ISIN du contrat Floriane 2 (Crédit Agricole/Predica)
$defaultCodes = [
    'FR0000120321', // L'Oréal
    'FR0010315770', // Amundi MSCI World
    'IE00BM67HK77', // iShares Global Clean Energy
    'LU0533033238', // Xtrackers MSCI World Health Care
    'FR0013412038', // Lyxor Green Bond
    'FR0010135103', // Amundi Patrimoine
    'LU0629459743', // Xtrackers MSCI World ESG
    'IE00BFNM3J75', // iShares MSCI World ESG Enhanced
    'FR0013412020', // Lyxor MSCI World ESG Leaders Extra
    'LU0274208692', // Xtrackers MSCI World IT
    'FR0010527275', // Amundi MSCI Emerging Markets
    'LU0908500753'  // Xtrackers MSCI USA
];
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Analyseur de Fonds d'Investissement</title>
    <link rel="stylesheet" href="assets/style.css">
</head>
<body>
    <div class="container">
        <!-- En-tête -->
        <div class="header">
            <h1>📊 Analyseur de Fonds</h1>
            <p>Analyse et comparaison de fonds d'investissement avec données Quantalys</p>
        </div>

        <?php if (isset($error)): ?>
            <div class="alert alert-danger">
                <?= htmlspecialchars($error) ?>
            </div>
        <?php endif; ?>

        <!-- Section de synchronisation -->
        <div class="sync-section">
            <form id="sync-form" class="sync-form">
                <input type="text" 
                       id="codes-isin" 
                       name="codes" 
                       placeholder="Codes ISIN séparés par des virgules (ex: FR0010315770, LU0274208692)"
                       value="<?= implode(', ', $defaultCodes) ?>">
                <button type="submit" class="btn btn-primary">Synchroniser les fonds</button>
                <button type="button" id="update-notes-btn" class="btn btn-secondary">Mettre à jour les notes</button>
            </form>
        </div>

        <!-- Tableau des fonds -->
        <div class="table-container">
            <table class="fonds-table">
                <thead>
                    <tr>
                        <th>Code ISIN</th>
                        <th>Nom du fonds</th>
                        <th>Performance 3 ans (%)</th>
                        <th>Volatilité (%)</th>
                        <th>Ratio Sharpe</th>
                        <th>Ratio Sortino</th>
                        <th>SRI</th>
                        <th>Note /10</th>
                        <th>Montant (€)</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($fonds as $fond): ?>
                        <tr class="<?= $fond['code_isin'] === 'EURO000000000' ? 'euro-row' : '' ?>">
                            <td><?= htmlspecialchars($fond['code_isin']) ?></td>
                            <td><?= htmlspecialchars($fond['nom']) ?></td>
                            <td><?= number_format($fond['performance_3ans'], 2) ?>%</td>
                            <td class="text-<?= Calculator::getVolatilityColor($fond['volatilite']) ?>">
                                <?= number_format($fond['volatilite'], 2) ?>%
                            </td>
                            <td class="<?= $fond['ratio_sharpe'] ? 'text-' . Calculator::getRatioColor($fond['ratio_sharpe']) : '' ?>">
                                <?= $fond['ratio_sharpe'] ? number_format($fond['ratio_sharpe'], 3) : '-' ?>
                            </td>
                            <td class="<?= $fond['ratio_sortino'] ? 'text-' . Calculator::getRatioColor($fond['ratio_sortino']) : '' ?>">
                                <?= $fond['ratio_sortino'] ? number_format($fond['ratio_sortino'], 3) : '-' ?>
                            </td>
                            <td><?= $fond['sri'] ?></td>
                            <td>
                                <span class="note-badge <?= getNoteBadgeClass($fond['note']) ?>">
                                    <?= number_format($fond['note'], 1) ?>
                                </span>
                            </td>
                            <td>
                                <input type="number" 
                                       class="montant-input" 
                                       data-isin="<?= htmlspecialchars($fond['code_isin']) ?>"
                                       min="0" 
                                       step="10" 
                                       value="0">
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>

        <!-- Récapitulatif -->
        <div class="recap-section">
            <h3>📈 Récapitulatif du portefeuille</h3>
            <div class="recap-grid">
                <div class="recap-item">
                    <h4>Montant total investi</h4>
                    <div id="montant-total" class="value">0,00 €</div>
                </div>
                <div class="recap-item">
                    <h4>SRI moyen pondéré</h4>
                    <div id="sri-moyen" class="value">0.0</div>
                </div>
                <div class="recap-item">
                    <h4>Nombre de fonds</h4>
                    <div class="value"><?= count($fonds) ?></div>
                </div>
                <div class="recap-item">
                    <h4>Note moyenne</h4>
                    <div class="value"><?= isset($stats['note_moyenne']) ? number_format($stats['note_moyenne'], 1) : '0.0' ?></div>
                </div>
            </div>
        </div>

        <!-- Préconisation d'investissement -->
        <?php if (!empty($recommendation)): ?>
        <div class="recommendation-section">
            <h3>💡 Préconisation d'investissement (1 000 €)</h3>
            <p>Allocation optimisée avec SRI cible de 4.0 et diversification sur 6 fonds maximum</p>
            
            <div class="recommendation-grid">
                <!-- Fonds Euro -->
                <div class="recommendation-item">
                    <h5>🏦 Fonds Euro (sécurité)</h5>
                    <div class="amount"><?= number_format($recommendation['fonds_euro']['montant'], 0) ?> €</div>
                    <div class="percentage"><?= $recommendation['fonds_euro']['pourcentage'] ?>% du portefeuille</div>
                </div>

                <!-- Autres fonds -->
                <?php foreach ($recommendation['autres_fonds'] as $item): ?>
                <div class="recommendation-item">
                    <h5><?= htmlspecialchars($item['fond']['nom']) ?></h5>
                    <div class="amount"><?= number_format($item['montant'], 0) ?> €</div>
                    <div class="percentage"><?= $item['pourcentage'] ?>% • SRI: <?= $item['fond']['sri'] ?></div>
                </div>
                <?php endforeach; ?>
            </div>

            <div style="margin-top: 20px; text-align: center; font-size: 18px;">
                <strong>SRI total du portefeuille : <?= number_format($recommendation['sri_total'], 1) ?></strong>
            </div>
        </div>
        <?php endif; ?>
    </div>

    <script src="assets/script.js"></script>
</body>
</html>

<?php
function getNoteBadgeClass($note) {
    if ($note >= 8) return 'note-excellent';
    if ($note >= 6) return 'note-good';
    if ($note >= 4) return 'note-average';
    return 'note-poor';
}
?>
