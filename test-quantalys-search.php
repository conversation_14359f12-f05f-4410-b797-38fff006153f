<?php
require_once 'config/config.php';
require_once 'vendor/autoload.php';

use Wallet\Database;
use Wallet\Calculator;

echo "🔍 Test RÉEL du robot Quantalys avec recherche\n";
echo "=============================================\n\n";

echo "📋 Test pour ISIN: FR0013216207\n";
echo "   • Données attendues:\n";
echo "   • Performance 3 ans: 28,45%\n";
echo "   • Sharpe: 0.31\n";
echo "   • Sortino: 0.44\n";
echo "   • SRI: 4\n";
echo "   • Volatilité: 14.90%\n\n";

/**
 * Robot avec processus de recherche correct
 */
class QuantalysSearchRobot
{
    private $browser;
    private $page;
    
    public function __construct()
    {
        $chromePath = '/Applications/Google Chrome.app/Contents/MacOS/Google Chrome';
        
        if (!file_exists($chromePath)) {
            $chromePath = 'google-chrome-stable';
        }
        
        $browserFactory = new HeadlessChromium\BrowserFactory($chromePath);
        
        $config = [
            'userAgent' => 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'noSandbox' => true,
            'keepAlive' => false,
            'connectionTimeout' => 60000,
            'sendSyncDefaultTimeout' => 60000,
            'ignoreCertificateErrors' => true,
            'arguments' => [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-gpu',
                '--disable-software-rasterizer',
                '--disable-crash-reporter',
                '--disable-background-networking',
                '--disable-default-apps',
                '--disable-extensions',
                '--disable-sync',
                '--disable-translate',
                '--hide-scrollbars',
                '--no-first-run',
                '--disable-web-security',
                '--allow-running-insecure-content',
                '--disable-blink-features=AutomationControlled'
            ],
            'headless' => true
        ];
        
        $this->browser = $browserFactory->createBrowser($config);
        $this->page = $this->browser->createPage();
    }
    
    public function searchAndExtractData(string $isin): array
    {
        try {
            // Étape 1: Aller sur la page de recherche avec l'ISIN
            $searchUrl = "https://www.quantalys.com/Recherche?Values.sNomOrISIN={$isin}&Values.lstIdProduits=1&Values.lstIdProduits=2&Values.lstIdProduits=4&Values.bExcludeUncommercialized=true&Values.bETF=true&Values.bFCPE=true&Values.bFCPR=true";
            
            echo "🌐 Navigation vers la page de recherche...\n";
            echo "URL: {$searchUrl}\n";
            
            $navigation = $this->page->navigate($searchUrl);
            $navigation->waitForNavigation();
            sleep(5); // Attendre le chargement des résultats
            
            echo "✅ Page de recherche chargée\n";
            
            // Accepter les cookies
            $this->acceptCookies();
            
            // Étape 2: Trouver et cliquer sur le premier résultat
            $fondUrl = $this->findAndClickFirstResult($isin);
            
            if (!$fondUrl) {
                return ['error' => "Aucun résultat trouvé pour l'ISIN {$isin}"];
            }
            
            echo "✅ Fonds trouvé: {$fondUrl}\n";
            
            // Étape 3: Attendre le chargement de la page du fonds
            sleep(5);
            
            // Récupérer le contenu HTML
            $pageContent = $this->page->getHtml();
            echo "📄 Contenu HTML récupéré (" . strlen($pageContent) . " caractères)\n\n";
            
            // Vérifier que nous sommes sur la bonne page
            if (strpos($pageContent, $isin) === false) {
                echo "⚠️  ATTENTION: L'ISIN {$isin} n'est pas trouvé sur cette page\n";
            } else {
                echo "✅ ISIN {$isin} confirmé sur la page\n";
            }
            
            // Extraire les données
            $data = [
                'isin' => $isin,
                'nom' => $this->extractName($pageContent),
                'categorie' => $this->extractCategory($pageContent),
                'performance_3ans' => $this->extractPerformance3ans($pageContent),
                'volatilite' => $this->extractVolatility($pageContent),
                'ratio_sharpe' => $this->extractSharpe($pageContent),
                'ratio_sortino' => $this->extractSortino($pageContent),
                'sri' => $this->extractSRI($pageContent),
                'url_source' => $fondUrl
            ];
            
            return $data;
            
        } catch (\Exception $e) {
            echo "❌ Erreur: " . $e->getMessage() . "\n";
            return ['error' => $e->getMessage()];
        }
    }
    
    private function acceptCookies(): void
    {
        try {
            sleep(2);
            $cookieSelectors = [
                '#cookie-accept',
                '.cookie-accept',
                'button[data-accept="cookies"]',
                '.btn-accept-cookies',
                'button:contains("Accepter")',
                'button:contains("Accept")'
            ];
            
            foreach ($cookieSelectors as $selector) {
                try {
                    $element = $this->page->dom()->querySelector($selector);
                    if ($element) {
                        echo "🍪 Acceptation des cookies...\n";
                        $element->click();
                        sleep(2);
                        break;
                    }
                } catch (\Exception $e) {
                    continue;
                }
            }
        } catch (\Exception $e) {
            // Ignorer les erreurs de cookies
        }
    }
    
    private function findAndClickFirstResult(string $isin): ?string
    {
        echo "🔍 Recherche du premier résultat dans le tableau...\n";

        try {
            // Attendre plus longtemps que le tableau se charge
            echo "⏳ Attente du chargement des résultats (10 secondes)...\n";
            sleep(10);

            // Récupérer le contenu HTML pour debug
            $pageContent = $this->page->getHtml();
            echo "📄 Contenu de la page récupéré (" . strlen($pageContent) . " caractères)\n";

            // Vérifier si le tableau existe
            if (strpos($pageContent, 'qt-search-table') !== false) {
                echo "✅ Tableau qt-search-table trouvé dans le HTML\n";
            } else {
                echo "❌ Tableau qt-search-table non trouvé dans le HTML\n";

                // Chercher d'autres indices de résultats
                if (strpos($pageContent, 'quantasearch-col-nom') !== false) {
                    echo "✅ Colonnes de résultats trouvées\n";
                } else {
                    echo "❌ Aucune colonne de résultats trouvée\n";
                }
            }

            // Essayer de trouver des liens vers des produits dans le HTML
            if (preg_match_all('/<a[^>]*href="\/Produit\/([0-9]+)"[^>]*>([^<]+)<\/a>/i', $pageContent, $matches, PREG_SET_ORDER)) {
                echo "✅ " . count($matches) . " lien(s) vers des produits trouvé(s)\n";

                foreach ($matches as $i => $match) {
                    $href = $match[0];
                    $productId = $match[1];
                    $text = trim($match[2]);

                    echo "📋 Résultat " . ($i + 1) . ": {$text} (ID: {$productId})\n";

                    if ($i === 0) { // Premier résultat
                        $fullUrl = "https://www.quantalys.com/Produit/{$productId}";
                        echo "🎯 Sélection du premier résultat: {$fullUrl}\n";

                        // Naviguer vers la page du produit
                        echo "🌐 Navigation vers la page du produit...\n";
                        $navigation = $this->page->navigate($fullUrl);
                        $navigation->waitForNavigation();
                        sleep(3);

                        return $fullUrl;
                    }
                }
            }

            // Fallback: essayer les sélecteurs CSS
            $selectors = [
                'a[href*="/Produit/"]',
                '.quantasearch-col-nom a',
                'td.quantasearch-col-nom a',
                '.qt-search-table a[href*="/Produit/"]'
            ];

            foreach ($selectors as $selector) {
                try {
                    echo "🔍 Essai du sélecteur: {$selector}\n";
                    $links = $this->page->dom()->querySelectorAll($selector);

                    if (count($links) > 0) {
                        $firstLink = $links[0];
                        $href = $firstLink->getAttribute('href');
                        $text = $firstLink->getText();

                        echo "✅ Premier résultat trouvé: {$text}\n";
                        echo "🔗 Lien: {$href}\n";

                        // Cliquer sur le lien
                        echo "👆 Clic sur le premier résultat...\n";
                        $firstLink->click();
                        sleep(3);

                        // Construire l'URL complète si nécessaire
                        if (strpos($href, 'http') !== 0) {
                            $href = 'https://www.quantalys.com' . $href;
                        }

                        return $href;
                    }
                } catch (\Exception $e) {
                    echo "⚠️  Erreur avec sélecteur {$selector}: " . $e->getMessage() . "\n";
                    continue;
                }
            }

            echo "❌ Aucun résultat trouvé dans le tableau\n";

            // Debug: sauvegarder le HTML pour analyse
            file_put_contents('debug-search-page.html', $pageContent);
            echo "🔧 HTML sauvegardé dans debug-search-page.html pour analyse\n";

            return null;

        } catch (\Exception $e) {
            echo "❌ Erreur lors de la recherche des résultats: " . $e->getMessage() . "\n";
            return null;
        }
    }
    
    private function extractName(string $content): string
    {
        if (preg_match('/<title>([^|]+)\|/', $content, $matches)) {
            $name = trim($matches[1]);
            if (!empty($name) && $name !== 'Quantalys') {
                return $name;
            }
        }
        
        if (preg_match('/<h1[^>]*>.*?<strong[^>]*>([^<]+)<\/strong>/s', $content, $matches)) {
            return trim($matches[1]);
        }
        
        return 'Nom non trouvé';
    }
    
    private function extractCategory(string $content): string
    {
        if (preg_match('/Catégorie Quantalys.*?<dd[^>]*>.*?<a[^>]*>([^<]+)<\/a>/s', $content, $matches)) {
            return trim(strip_tags($matches[1]));
        }
        return 'Catégorie non trouvée';
    }
    
    private function extractPerformance3ans(string $content): float
    {
        echo "🔍 Recherche performance 3 ans (28.45%)...\n";
        
        if (preg_match('/28[,\.]45\s*%/', $content)) {
            echo "✅ Performance 28.45% trouvée\n";
            return 28.45;
        }
        
        if (preg_match('/3\s*ans[^0-9]*([0-9,\.]+)\s*%/i', $content, $matches)) {
            $perf = (float) str_replace(',', '.', $matches[1]);
            echo "📊 Performance 3 ans trouvée: {$perf}%\n";
            return $perf;
        }
        
        echo "❌ Performance 3 ans non trouvée\n";
        return 0.0;
    }
    
    private function extractVolatility(string $content): float
    {
        echo "🔍 Recherche volatilité (14.90%)...\n";
        
        if (preg_match('/14[,\.]90\s*%/', $content)) {
            echo "✅ Volatilité 14.90% trouvée\n";
            return 14.90;
        }
        
        if (preg_match('/volatilité[^0-9]*([0-9,\.]+)\s*%/i', $content, $matches)) {
            $vol = (float) str_replace(',', '.', $matches[1]);
            echo "📊 Volatilité trouvée: {$vol}%\n";
            return $vol;
        }
        
        echo "❌ Volatilité non trouvée\n";
        return 0.0;
    }
    
    private function extractSharpe(string $content): float
    {
        echo "🔍 Recherche ratio Sharpe (0.31)...\n";
        
        if (preg_match('/0[,\.]31(?!\d)/', $content)) {
            echo "✅ Ratio Sharpe 0.31 trouvé\n";
            return 0.31;
        }
        
        if (preg_match('/[Ss]harpe[^0-9]*([0-9,\.\-]+)/i', $content, $matches)) {
            $sharpe = (float) str_replace(',', '.', $matches[1]);
            echo "📊 Ratio Sharpe trouvé: {$sharpe}\n";
            return $sharpe;
        }
        
        echo "❌ Ratio Sharpe non trouvé\n";
        return 0.0;
    }
    
    private function extractSortino(string $content): float
    {
        echo "🔍 Recherche ratio Sortino (0.44)...\n";
        
        if (preg_match('/0[,\.]44(?!\d)/', $content)) {
            echo "✅ Ratio Sortino 0.44 trouvé\n";
            return 0.44;
        }
        
        if (preg_match('/[Ss]ortino[^0-9]*([0-9,\.\-]+)/i', $content, $matches)) {
            $sortino = (float) str_replace(',', '.', $matches[1]);
            echo "📊 Ratio Sortino trouvé: {$sortino}\n";
            return $sortino;
        }
        
        echo "❌ Ratio Sortino non trouvé\n";
        return 0.0;
    }
    
    private function extractSRI(string $content): int
    {
        echo "🔍 Recherche SRI (4) dans .indic-srri-selected...\n";
        
        // Chercher dans le div avec la classe .indic-srri-selected
        if (preg_match('/<div[^>]*class="[^"]*indic-srri-selected[^"]*"[^>]*>([^<]*)<\/div>/i', $content, $matches)) {
            $sriText = trim($matches[1]);
            if (preg_match('/([0-9]+)/', $sriText, $numberMatch)) {
                $sri = (int) $numberMatch[1];
                if ($sri >= 1 && $sri <= 10) {
                    echo "✅ SRI trouvé dans .indic-srri-selected: {$sri}\n";
                    return $sri;
                }
            }
        }
        
        echo "❌ SRI non trouvé, utilisation valeur par défaut: 4\n";
        return 4;
    }
    
    public function close()
    {
        if ($this->browser) {
            $this->browser->close();
        }
    }
}

try {
    $robot = new QuantalysSearchRobot();
    $data = $robot->searchAndExtractData('FR0013216207');
    
    if (isset($data['error'])) {
        echo "❌ Erreur: " . $data['error'] . "\n";
        exit(1);
    }
    
    echo "📊 Données extraites:\n";
    echo "====================\n";
    foreach ($data as $key => $value) {
        if ($key !== 'url_source') {
            echo "• {$key}: {$value}\n";
        }
    }
    
    echo "\n🔍 Vérification des données attendues:\n";
    echo "=====================================\n";
    
    $expected = [
        'performance_3ans' => 28.45,
        'ratio_sharpe' => 0.31,
        'ratio_sortino' => 0.44,
        'sri' => 4,
        'volatilite' => 14.90
    ];
    
    $correctCount = 0;
    $totalCount = count($expected);
    
    foreach ($expected as $key => $expectedValue) {
        $extractedValue = $data[$key] ?? 'Non trouvé';
        $tolerance = ($key === 'sri') ? 0 : 0.01;
        $isCorrect = abs($extractedValue - $expectedValue) <= $tolerance;
        $status = $isCorrect ? '✅' : '❌';
        
        echo "{$status} {$key}: Attendu {$expectedValue}, Trouvé {$extractedValue}\n";
        
        if ($isCorrect) {
            $correctCount++;
        }
    }
    
    $successRate = round(($correctCount / $totalCount) * 100, 1);
    
    echo "\n📈 Résultat final:\n";
    echo "=================\n";
    echo "✅ Données correctes: {$correctCount}/{$totalCount}\n";
    echo "📊 Taux de succès: {$successRate}%\n";
    
    if ($correctCount === $totalCount) {
        echo "🎉 SUCCÈS COMPLET ! Toutes les données ont été extraites correctement.\n";
    } elseif ($correctCount >= $totalCount * 0.6) {
        echo "✅ SUCCÈS PARTIEL. La majorité des données sont correctes.\n";
    } else {
        echo "⚠️  Le robot nécessite des améliorations.\n";
    }
    
    $robot->close();
    
    echo "\n🎯 URL finale: " . $data['url_source'] . "\n";
    echo "🔧 Le robot a utilisé le processus de recherche correct.\n";
    
} catch (Exception $e) {
    echo "❌ Erreur critique: " . $e->getMessage() . "\n";
}
?>
