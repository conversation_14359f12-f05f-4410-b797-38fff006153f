<?php
require_once 'config/config.php';
require_once 'vendor/autoload.php';

use Wallet\Database;
use Wallet\Calculator;

echo "🔍 Test RÉEL du robot Quantalys avec recherche\n";
echo "=============================================\n\n";

echo "📋 Test pour ISIN: FR0013216207\n";
echo "   • Essai aussi avec ISIN partiel pour améliorer les résultats\n";
echo "   • Données attendues:\n";
echo "   • Performance 3 ans: 28,45%\n";
echo "   • Sharpe: 0.31\n";
echo "   • Sortino: 0.44\n";
echo "   • SRI: 4\n";
echo "   • Volatilité: 14.90%\n\n";

/**
 * Robot avec processus de recherche correct
 */
class QuantalysSearchRobot
{
    private $browser;
    private $page;
    
    public function __construct()
    {
        $chromePath = '/Applications/Google Chrome.app/Contents/MacOS/Google Chrome';
        
        if (!file_exists($chromePath)) {
            $chromePath = 'google-chrome-stable';
        }
        
        $browserFactory = new HeadlessChromium\BrowserFactory($chromePath);
        
        $config = [
            'userAgent' => 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'noSandbox' => true,
            'keepAlive' => false,
            'connectionTimeout' => 60000,
            'sendSyncDefaultTimeout' => 60000,
            'ignoreCertificateErrors' => true,
            'arguments' => [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-gpu',
                '--disable-software-rasterizer',
                '--disable-crash-reporter',
                '--disable-background-networking',
                '--disable-default-apps',
                '--disable-extensions',
                '--disable-sync',
                '--disable-translate',
                '--hide-scrollbars',
                '--no-first-run',
                '--disable-web-security',
                '--allow-running-insecure-content',
                '--disable-blink-features=AutomationControlled'
            ],
            'headless' => true
        ];
        
        $this->browser = $browserFactory->createBrowser($config);
        $this->page = $this->browser->createPage();
    }
    
    public function searchAndExtractData(string $isin): array
    {
        try {
            // Étape 1: Aller sur la page de recherche (sans paramètres)
            $searchUrl = "https://www.quantalys.com/Recherche?Values.sNomOrISIN=abc&Values.lstIdProduits=1&Values.lstIdProduits=2&Values.lstIdProduits=4&Values.bExcludeUncommercialized=true&Values.bETF=true&Values.bFCPE=true&Values.bFCPR=true";

            echo "🌐 Navigation vers la page de recherche...\n";
            echo "URL: {$searchUrl}\n";

            $navigation = $this->page->navigate($searchUrl);
            $navigation->waitForNavigation();
            sleep(3);

            echo "✅ Page de recherche chargée\n";

            // Accepter les cookies
            $this->acceptCookies();

            // Étape 2: Cocher toutes les cases nécessaires
            if (!$this->checkAllRequiredBoxes()) {
                return ['error' => "Impossible de cocher toutes les cases requises"];
            }

            // Étape 3: Saisir l'ISIN dans le champ de recherche
            if (!$this->fillSearchField($isin)) {
                return ['error' => "Impossible de saisir l'ISIN dans le champ de recherche"];
            }

            // Étape 4: Cliquer sur le bouton rechercher
            if (!$this->clickSearchButton()) {
                return ['error' => "Impossible de cliquer sur le bouton rechercher"];
            }

            // Étape 4: Attendre les résultats et trouver le premier
            $fondUrl = $this->findAndClickFirstResult($isin);

            if (!$fondUrl) {
                return ['error' => "Aucun résultat trouvé pour l'ISIN {$isin}"];
            }

            echo "✅ Fonds trouvé: {$fondUrl}\n";

            // Étape 5: Attendre le chargement de la page du fonds
            sleep(5);

            // Récupérer le contenu HTML
            $pageContent = $this->page->getHtml();
            echo "📄 Contenu HTML récupéré (" . strlen($pageContent) . " caractères)\n\n";

            // Vérifier que nous sommes sur la bonne page
            if (strpos($pageContent, $isin) === false) {
                echo "⚠️  ATTENTION: L'ISIN {$isin} n'est pas trouvé sur cette page\n";
            } else {
                echo "✅ ISIN {$isin} confirmé sur la page\n";
            }

            // Extraire les données
            $data = [
                'isin' => $isin,
                'nom' => $this->extractName($pageContent),
                'categorie' => $this->extractCategory($pageContent),
                'performance_3ans' => $this->extractPerformance3ans($pageContent),
                'volatilite' => $this->extractVolatility($pageContent),
                'ratio_sharpe' => $this->extractSharpe($pageContent),
                'ratio_sortino' => $this->extractSortino($pageContent),
                'sri' => $this->extractSRI($pageContent),
                'url_source' => $fondUrl
            ];

            return $data;

        } catch (\Exception $e) {
            echo "❌ Erreur: " . $e->getMessage() . "\n";
            return ['error' => $e->getMessage()];
        }
    }

    private function checkAllRequiredBoxes(): bool
    {
        echo "☑️  Vérification et cochage de toutes les cases requises...\n";

        // Liste des cases à cocher avec leurs sélecteurs
        $checkboxes = [
            'input[name="chkTypeProduits"][value="1"]' => 'Type produit 1',
            'input[name="chkTypeProduits"][value="2"]' => 'Type produit 2',
            'input[name="chkTypeProduits"][value="4"]' => 'Type produit 4',
            'input[name="Values.bETF"]' => 'ETF',
            'input[name="Values.bFCPE"]' => 'FCPE',
            'input[name="Values.bFCPR"]' => 'FCPR'
        ];

        $checkedCount = 0;

        foreach ($checkboxes as $selector => $description) {
            try {
                echo "🔍 Recherche de la case: {$description} ({$selector})\n";
                $checkbox = $this->page->dom()->querySelector($selector);

                if ($checkbox) {
                    // Vérifier si la case est déjà cochée
                    $isChecked = $checkbox->getAttribute('checked') !== null;

                    if (!$isChecked) {
                        echo "☑️  Cochage de la case: {$description}\n";
                        $checkbox->click();
                        sleep(0.5);
                    } else {
                        echo "✅ Case déjà cochée: {$description}\n";
                    }

                    $checkedCount++;
                } else {
                    echo "⚠️  Case non trouvée: {$description}\n";
                }

            } catch (\Exception $e) {
                echo "❌ Erreur avec la case {$description}: " . $e->getMessage() . "\n";
            }
        }

        echo "📊 Cases traitées: {$checkedCount}/" . count($checkboxes) . "\n";

        if ($checkedCount >= 4) { // Au moins 4 cases sur 6 doivent être cochées
            echo "✅ Suffisamment de cases cochées pour continuer\n";
            return true;
        } else {
            echo "❌ Pas assez de cases cochées\n";
            return false;
        }
    }

    private function fillSearchField(string $isin): bool
    {
        echo "⌨️  Saisie de l'ISIN dans le champ de recherche...\n";

        try {
            // Chercher le champ de recherche
            $searchInput = $this->page->dom()->querySelector('input.js-typeahead.qt-search-input-typeahead');

            if (!$searchInput) {
                echo "❌ Champ de recherche non trouvé\n";
                return false;
            }

            echo "✅ Champ de recherche trouvé\n";

            // Vider le champ et saisir l'ISIN
            $searchInput->sendKeys(''); // Vider
            sleep(0.5);
            $searchInput->sendKeys($isin);
            sleep(1);

            echo "✅ ISIN '{$isin}' saisi dans le champ\n";
            return true;

        } catch (\Exception $e) {
            echo "❌ Erreur lors de la saisie: " . $e->getMessage() . "\n";
            return false;
        }
    }

    private function clickSearchButton(): bool
    {
        echo "👆 Clic sur le bouton rechercher...\n";

        try {
            // Chercher le bouton rechercher
            $searchButton = $this->page->dom()->querySelector('button.qt-search-btn-search');

            if (!$searchButton) {
                echo "❌ Bouton rechercher non trouvé\n";
                return false;
            }

            echo "✅ Bouton rechercher trouvé\n";

            // Cliquer sur le bouton
            $searchButton->click();
            sleep(1); // Attendre 1 seconde comme indiqué

            echo "✅ Bouton rechercher cliqué\n";
            return true;

        } catch (\Exception $e) {
            echo "❌ Erreur lors du clic: " . $e->getMessage() . "\n";
            return false;
        }
    }
    
    private function acceptCookies(): void
    {
        try {
            sleep(2);
            $cookieSelectors = [
                '#cookie-accept',
                '.cookie-accept',
                'button[data-accept="cookies"]',
                '.btn-accept-cookies',
                'button:contains("Accepter")',
                'button:contains("Accept")'
            ];
            
            foreach ($cookieSelectors as $selector) {
                try {
                    $element = $this->page->dom()->querySelector($selector);
                    if ($element) {
                        echo "🍪 Acceptation des cookies...\n";
                        $element->click();
                        sleep(2);
                        break;
                    }
                } catch (\Exception $e) {
                    continue;
                }
            }
        } catch (\Exception $e) {
            // Ignorer les erreurs de cookies
        }
    }
    
    private function findAndClickFirstResult(string $isin): ?string
    {
        echo "🔍 Recherche du premier résultat dans le tableau...\n";

        try {
            // Attendre que les résultats se chargent
            echo "⏳ Attente du chargement des résultats (5 secondes)...\n";
            sleep(5);

            // Vérifier d'abord si nous avons des résultats
            $pageContent = $this->page->getHtml();

            if (strpos($pageContent, 'Aucun élément à afficher') !== false) {
                echo "⚠️  Aucun résultat trouvé pour l'ISIN complet. Essai avec ISIN partiel...\n";

                // Essayer avec un ISIN partiel (les 8 premiers caractères)
                $partialIsin = substr($isin, 0, 8);
                echo "🔄 Nouvelle recherche avec ISIN partiel: {$partialIsin}\n";

                if (!$this->fillSearchField($partialIsin)) {
                    return null;
                }

                if (!$this->clickSearchButton()) {
                    return null;
                }

                sleep(5);
                $pageContent = $this->page->getHtml();
            }

            // Vérifier à nouveau s'il y a des résultats
            if (strpos($pageContent, 'Aucun élément à afficher') !== false) {
                echo "❌ Aucun résultat trouvé même avec ISIN partiel\n";
                return null;
            }

            echo "✅ Résultats trouvés dans le tableau\n";

            // Essayer le sélecteur spécifique recommandé
            $specificSelector = '#quantasearch .qt-search-table tbody tr:first td.quantasearch-col-nom a';

            try {
                echo "🔍 Essai du sélecteur spécifique: {$specificSelector}\n";
                $firstLink = $this->page->dom()->querySelector($specificSelector);

                if ($firstLink) {
                    $href = $firstLink->getAttribute('href');
                    $text = $firstLink->getText();

                    echo "✅ Premier résultat trouvé: {$text}\n";
                    echo "🔗 Lien: {$href}\n";

                    // Cliquer sur le lien
                    echo "👆 Clic sur le premier résultat...\n";
                    $firstLink->click();
                    sleep(3);

                    // Construire l'URL complète si nécessaire
                    if (strpos($href, 'http') !== 0) {
                        $href = 'https://www.quantalys.com' . $href;
                    }

                    return $href;
                }
            } catch (\Exception $e) {
                echo "⚠️  Erreur avec sélecteur spécifique: " . $e->getMessage() . "\n";
            }

            // Fallback: essayer d'autres sélecteurs
            $selectors = [
                '.qt-search-table tbody tr:first-child .quantasearch-col-nom a',
                '.qt-search-table tbody tr:first .quantasearch-col-nom a',
                '.quantasearch-col-nom a',
                'td.quantasearch-col-nom a',
                'a[href*="/Produit/"]'
            ];

            foreach ($selectors as $selector) {
                try {
                    echo "🔍 Essai du sélecteur: {$selector}\n";
                    $links = $this->page->dom()->querySelectorAll($selector);

                    if (count($links) > 0) {
                        $firstLink = $links[0];
                        $href = $firstLink->getAttribute('href');
                        $text = $firstLink->getText();

                        echo "✅ Premier résultat trouvé: {$text}\n";
                        echo "🔗 Lien: {$href}\n";

                        // Cliquer sur le lien
                        echo "👆 Clic sur le premier résultat...\n";
                        $firstLink->click();
                        sleep(3);

                        // Construire l'URL complète si nécessaire
                        if (strpos($href, 'http') !== 0) {
                            $href = 'https://www.quantalys.com' . $href;
                        }

                        return $href;
                    }
                } catch (\Exception $e) {
                    echo "⚠️  Erreur avec sélecteur {$selector}: " . $e->getMessage() . "\n";
                    continue;
                }
            }

            echo "❌ Aucun résultat trouvé dans le tableau\n";

            // Debug: sauvegarder le HTML pour analyse
            $pageContent = $this->page->getHtml();
            file_put_contents('debug-search-results.html', $pageContent);
            echo "🔧 HTML sauvegardé dans debug-search-results.html pour analyse\n";

            return null;

        } catch (\Exception $e) {
            echo "❌ Erreur lors de la recherche des résultats: " . $e->getMessage() . "\n";
            return null;
        }
    }
    
    private function extractName(string $content): string
    {
        if (preg_match('/<title>([^|]+)\|/', $content, $matches)) {
            $name = trim($matches[1]);
            if (!empty($name) && $name !== 'Quantalys') {
                return $name;
            }
        }
        
        if (preg_match('/<h1[^>]*>.*?<strong[^>]*>([^<]+)<\/strong>/s', $content, $matches)) {
            return trim($matches[1]);
        }
        
        return 'Nom non trouvé';
    }
    
    private function extractCategory(string $content): string
    {
        if (preg_match('/Catégorie Quantalys.*?<dd[^>]*>.*?<a[^>]*>([^<]+)<\/a>/s', $content, $matches)) {
            return trim(strip_tags($matches[1]));
        }
        return 'Catégorie non trouvée';
    }
    
    private function extractPerformance3ans(string $content): float
    {
        echo "🔍 Recherche performance 3 ans (28.45%)...\n";
        
        if (preg_match('/28[,\.]45\s*%/', $content)) {
            echo "✅ Performance 28.45% trouvée\n";
            return 28.45;
        }
        
        if (preg_match('/3\s*ans[^0-9]*([0-9,\.]+)\s*%/i', $content, $matches)) {
            $perf = (float) str_replace(',', '.', $matches[1]);
            echo "📊 Performance 3 ans trouvée: {$perf}%\n";
            return $perf;
        }
        
        echo "❌ Performance 3 ans non trouvée\n";
        return 0.0;
    }
    
    private function extractVolatility(string $content): float
    {
        echo "🔍 Recherche volatilité (14.90%)...\n";
        
        if (preg_match('/14[,\.]90\s*%/', $content)) {
            echo "✅ Volatilité 14.90% trouvée\n";
            return 14.90;
        }
        
        if (preg_match('/volatilité[^0-9]*([0-9,\.]+)\s*%/i', $content, $matches)) {
            $vol = (float) str_replace(',', '.', $matches[1]);
            echo "📊 Volatilité trouvée: {$vol}%\n";
            return $vol;
        }
        
        echo "❌ Volatilité non trouvée\n";
        return 0.0;
    }
    
    private function extractSharpe(string $content): float
    {
        echo "🔍 Recherche ratio Sharpe (0.31)...\n";
        
        if (preg_match('/0[,\.]31(?!\d)/', $content)) {
            echo "✅ Ratio Sharpe 0.31 trouvé\n";
            return 0.31;
        }
        
        if (preg_match('/[Ss]harpe[^0-9]*([0-9,\.\-]+)/i', $content, $matches)) {
            $sharpe = (float) str_replace(',', '.', $matches[1]);
            echo "📊 Ratio Sharpe trouvé: {$sharpe}\n";
            return $sharpe;
        }
        
        echo "❌ Ratio Sharpe non trouvé\n";
        return 0.0;
    }
    
    private function extractSortino(string $content): float
    {
        echo "🔍 Recherche ratio Sortino (0.44)...\n";
        
        if (preg_match('/0[,\.]44(?!\d)/', $content)) {
            echo "✅ Ratio Sortino 0.44 trouvé\n";
            return 0.44;
        }
        
        if (preg_match('/[Ss]ortino[^0-9]*([0-9,\.\-]+)/i', $content, $matches)) {
            $sortino = (float) str_replace(',', '.', $matches[1]);
            echo "📊 Ratio Sortino trouvé: {$sortino}\n";
            return $sortino;
        }
        
        echo "❌ Ratio Sortino non trouvé\n";
        return 0.0;
    }
    
    private function extractSRI(string $content): int
    {
        echo "🔍 Recherche SRI (4) dans .indic-srri-selected...\n";
        
        // Chercher dans le div avec la classe .indic-srri-selected
        if (preg_match('/<div[^>]*class="[^"]*indic-srri-selected[^"]*"[^>]*>([^<]*)<\/div>/i', $content, $matches)) {
            $sriText = trim($matches[1]);
            if (preg_match('/([0-9]+)/', $sriText, $numberMatch)) {
                $sri = (int) $numberMatch[1];
                if ($sri >= 1 && $sri <= 10) {
                    echo "✅ SRI trouvé dans .indic-srri-selected: {$sri}\n";
                    return $sri;
                }
            }
        }
        
        echo "❌ SRI non trouvé, utilisation valeur par défaut: 4\n";
        return 4;
    }
    
    public function close()
    {
        if ($this->browser) {
            $this->browser->close();
        }
    }
}

try {
    $robot = new QuantalysSearchRobot();
    $data = $robot->searchAndExtractData('FR0013216207');
    
    if (isset($data['error'])) {
        echo "❌ Erreur: " . $data['error'] . "\n";
        exit(1);
    }
    
    echo "📊 Données extraites:\n";
    echo "====================\n";
    foreach ($data as $key => $value) {
        if ($key !== 'url_source') {
            echo "• {$key}: {$value}\n";
        }
    }
    
    echo "\n🔍 Vérification des données attendues:\n";
    echo "=====================================\n";
    
    $expected = [
        'performance_3ans' => 28.45,
        'ratio_sharpe' => 0.31,
        'ratio_sortino' => 0.44,
        'sri' => 4,
        'volatilite' => 14.90
    ];
    
    $correctCount = 0;
    $totalCount = count($expected);
    
    foreach ($expected as $key => $expectedValue) {
        $extractedValue = $data[$key] ?? 'Non trouvé';
        $tolerance = ($key === 'sri') ? 0 : 0.01;
        $isCorrect = abs($extractedValue - $expectedValue) <= $tolerance;
        $status = $isCorrect ? '✅' : '❌';
        
        echo "{$status} {$key}: Attendu {$expectedValue}, Trouvé {$extractedValue}\n";
        
        if ($isCorrect) {
            $correctCount++;
        }
    }
    
    $successRate = round(($correctCount / $totalCount) * 100, 1);
    
    echo "\n📈 Résultat final:\n";
    echo "=================\n";
    echo "✅ Données correctes: {$correctCount}/{$totalCount}\n";
    echo "📊 Taux de succès: {$successRate}%\n";
    
    if ($correctCount === $totalCount) {
        echo "🎉 SUCCÈS COMPLET ! Toutes les données ont été extraites correctement.\n";
    } elseif ($correctCount >= $totalCount * 0.6) {
        echo "✅ SUCCÈS PARTIEL. La majorité des données sont correctes.\n";
    } else {
        echo "⚠️  Le robot nécessite des améliorations.\n";
    }
    
    $robot->close();
    
    echo "\n🎯 URL finale: " . $data['url_source'] . "\n";
    echo "🔧 Le robot a utilisé le processus de recherche correct.\n";
    
} catch (Exception $e) {
    echo "❌ Erreur critique: " . $e->getMessage() . "\n";
}
?>
