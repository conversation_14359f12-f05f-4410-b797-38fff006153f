<?php
require_once 'config/config.php';
require_once 'vendor/autoload.php';

use Wallet\Database;
use Wallet\FondsManager;
use Wallet\QuantalysAPI;
use Wallet\Calculator;

echo "🧪 Test de l'application Analyseur de Fonds\n";
echo "==========================================\n\n";

try {
    // Test 1: Connexion à la base de données
    echo "1. Test de connexion à la base de données...\n";
    $db = Database::getInstance();
    echo "✅ Connexion réussie\n\n";

    // Test 2: Initialisation du schéma
    echo "2. Initialisation du schéma de base de données...\n";
    $fondsManager = new FondsManager();
    $fondsManager->initializeDatabase();
    echo "✅ Schéma initialisé\n\n";

    // Test 3: Test de l'API simulée
    echo "3. Test de l'API Quantalys (simulée)...\n";
    $api = new QuantalysAPI();
    $testFond = $api->getFondsByISIN('FR0010315770');
    if ($testFond) {
        echo "✅ API fonctionnelle - Fond trouvé: " . $testFond['nom'] . "\n\n";
    } else {
        echo "❌ Erreur API\n\n";
    }

    // Test 4: Synchronisation des fonds
    echo "4. Test de synchronisation des fonds...\n";
    $codes = ['FR0010315770', 'FR0013412038', 'LU0274208692'];
    $results = $fondsManager->syncFonds($codes);
    echo "✅ " . count($results) . " fonds traités\n\n";

    // Test 5: Récupération des fonds
    echo "5. Test de récupération des fonds...\n";
    $fonds = $fondsManager->getAllFonds();
    echo "✅ " . count($fonds) . " fonds en base de données\n\n";

    // Test 6: Calcul des notes
    echo "6. Test de calcul des notes...\n";
    foreach ($fonds as $fond) {
        if ($fond['code_isin'] !== 'EURO000000000') {
            $note = Calculator::calculateNote($fond);
            echo "   - " . $fond['nom'] . ": " . $note . "/10\n";
        }
    }
    echo "✅ Calculs terminés\n\n";

    // Test 7: Génération de préconisation
    echo "7. Test de génération de préconisation...\n";
    $recommendation = Calculator::generateRecommendation($fonds, 1000);
    echo "✅ Préconisation générée pour 1000€\n";
    echo "   - SRI total: " . $recommendation['sri_total'] . "\n";
    echo "   - Fonds Euro: " . $recommendation['fonds_euro']['montant'] . "€\n";
    echo "   - Autres fonds: " . count($recommendation['autres_fonds']) . "\n\n";

    echo "🎉 Tous les tests sont passés avec succès !\n";
    echo "Vous pouvez maintenant accéder à l'application via index.php\n";

} catch (Exception $e) {
    echo "❌ Erreur: " . $e->getMessage() . "\n";
    echo "Trace: " . $e->getTraceAsString() . "\n";
}
?>
