<?php
require_once 'config/config.php';

echo "🚀 Configuration de l'application Analyseur de Fonds\n";
echo "===================================================\n\n";

try {
    // Connexion sans spécifier la base de données pour la créer
    echo "1. Connexion au serveur MySQL...\n";
    $pdo = new PDO(
        "mysql:host=" . DB_HOST . ";port=" . DB_PORT . ";charset=utf8mb4",
        DB_LOGIN,
        DB_PASS,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        ]
    );
    echo "✅ Connexion réussie\n\n";

    // Créer la base de données si elle n'existe pas
    echo "2. Création de la base de données '" . DB_NAME . "'...\n";
    $pdo->exec("CREATE DATABASE IF NOT EXISTS `" . DB_NAME . "` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "✅ Base de données créée ou existe déjà\n\n";

    // Sélectionner la base de données
    echo "3. Sélection de la base de données...\n";
    $pdo->exec("USE `" . DB_NAME . "`");
    echo "✅ Base de données sélectionnée\n\n";

    // Exécuter le schéma
    echo "4. Création des tables...\n";
    $schemaPath = __DIR__ . '/database/schema.sql';
    if (!file_exists($schemaPath)) {
        throw new Exception("Fichier de schéma non trouvé : {$schemaPath}");
    }

    $sql = file_get_contents($schemaPath);
    $statements = array_filter(array_map('trim', explode(';', $sql)));

    foreach ($statements as $statement) {
        if (!empty($statement)) {
            $pdo->exec($statement);
        }
    }
    echo "✅ Tables créées avec succès\n\n";

    // Vérifier que tout fonctionne
    echo "5. Vérification de l'installation...\n";
    $result = $pdo->query("SELECT COUNT(*) as count FROM fonds")->fetch();
    echo "✅ " . $result['count'] . " fonds en base de données\n\n";

    echo "🎉 Configuration terminée avec succès !\n";
    echo "Vous pouvez maintenant lancer 'php test.php' pour tester l'application.\n";

} catch (Exception $e) {
    echo "❌ Erreur: " . $e->getMessage() . "\n";
    exit(1);
}
?>
